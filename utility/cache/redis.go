package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gogf/gf/v2/database/gredis"
	"time"
)

// RedisCacheMiddleware 基于goframe gredis的缓存中间件
type RedisCacheMiddleware struct {
	client *gredis.Redis // gredis实例
	ttl    time.Duration // 超时时间
}

// NewRedisCacheMiddleware 创建中间件实例
func NewRedisCacheMiddleware(client *gredis.Redis, ttl time.Duration) *RedisCacheMiddleware {
	return &RedisCacheMiddleware{
		client: client,
		ttl:    ttl,
	}
}

// key生成规则
func (r *RedisCacheMiddleware) key(typ string, id interface{}) string {
	return fmt.Sprintf("%s:%v", typ, id)
}
func (r *RedisCacheMiddleware) SetString(ctx context.Context, typ string, id interface{}, value string) error {
	key := r.key(typ, id)
	return r.client.SetEX(ctx, key, value, int64(int(r.ttl.Seconds())))
}

// GetString 获取字符串
func (r *RedisCacheMiddleware) GetString(ctx context.Context, typ string, id interface{}) (string, bool, error) {
	key := r.key(typ, id)
	val, err := r.client.Get(ctx, key)
	if err != nil {
		return "", false, err
	}
	return val.String(), true, nil
}

// Set 写入缓存，value支持结构体、数组等
func (r *RedisCacheMiddleware) SetList(ctx context.Context, typ string, id interface{}, value interface{}) error {
	key := r.key(typ, id)
	bytes, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.client.SetEX(ctx, key, bytes, int64(int(r.ttl.Seconds())))
}

// Get 获取缓存数据，resultPtr为指针类型（如 *Article 或 *[]Article）
func (r *RedisCacheMiddleware) GetList(ctx context.Context, typ string, id interface{}, resultPtr interface{}) (bool, error) {
	key := r.key(typ, id)
	val, err := r.client.Get(ctx, key)
	if err != nil {
		return false, err
	}
	if val.String() == "" {
		return false, nil
	}
	return true, json.Unmarshal([]byte(val.String()), resultPtr)
}

func (r *RedisCacheMiddleware) SetListForever(ctx context.Context, typ string, id interface{}, value interface{}) (isSuccess bool, err error) {
	key := r.key(typ, id)
	bytes, err := json.Marshal(value)
	if err != nil {
		return false, err
	}
	return r.client.SetNX(ctx, key, bytes)
}

// Delete 删除缓存
func (r *RedisCacheMiddleware) Delete(ctx context.Context, typ string, id interface{}) error {
	key := r.key(typ, id)
	_, err := r.client.Del(ctx, key)
	return err
}
