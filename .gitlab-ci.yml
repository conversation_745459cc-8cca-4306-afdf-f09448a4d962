default:
  tags:
    - dev

stages:
  - build
  - deploy

# 项目变量
variables:
  GO_VERSION: "1.23"
  DEPLOY_USER: "halal"
  SSH_PORT: 2542


# 构建的公共配置
.build_template: &build_template
  stage: build
  image: golang:${GO_VERSION}-alpine
  parallel:
    matrix:
      - BINARY_NAME: ["file-storage-svc","islamic-content-svc","mall-svc","notify-svc","payment-svc","user-account-svc","wealth-charity-svc"]
  variables:
    SERVICE_NAME: "${BINARY_NAME%-svc}"
    DEPLOY_PATH: "/data/server/supervisor/${SERVICE_NAME}"
    APP_DIR: "app/${BINARY_NAME}"
  cache:
    key: ${CI_COMMIT_REF_SLUG}-${BINARY_NAME}
    paths:
      - out/
    policy: push
  before_script:
    - go version
    - go env -w GOPROXY=https://goproxy.cn,direct
    - mkdir -p out
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk add --no-cache git
    # 检查是否有变更
    - |
      echo "检查是否有变更"
      if [ -n "$CI_COMMIT_BEFORE_SHA" ] && [ "$CI_COMMIT_BEFORE_SHA" != "0000000000000000000000000000000000000000" ]; then
        CHANGED_FILES=$(git diff --name-only $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA)
        echo "变更的文件:"
        echo "$CHANGED_FILES"

        # 检查当前服务目录是否有变更
        if echo "$CHANGED_FILES" | grep -q "^${APP_DIR}/"; then
          echo "检测到 ${BINARY_NAME} 有变更，执行构建"
          export SHOULD_BUILD="true"
        else
          echo "${BINARY_NAME} 无变更，跳过构建"
          export SHOULD_BUILD="false"
        fi
      else
        # 首次提交或无法获取前一个commit时，默认构建
        echo "无法检测变更，默认执行构建"
        export SHOULD_BUILD="true"
      fi
  script:
    #- cd app/$BINARY_NAME && go mod tidy ; go build -o ../../out/$BINARY_NAME ; cd ../..
    - |
      if [ "$SHOULD_BUILD" = "true" ]; then
        cd ${APP_DIR} && go mod tidy && go build -o ../../out/$BINARY_NAME
        cd ../..
        echo "构建完成: ${BINARY_NAME}"
      else
        echo "跳过构建: ${BINARY_NAME}"
        # 如果跳过构建，确保out目录存在（为了缓存）
        mkdir -p out
        touch out/.skip_build
      fi


# 发布的公共配置
.deploy_template: &deploy_template
  image: alpine:latest
  parallel:
    matrix:
      - BINARY_NAME: ["file-storage-svc","islamic-content-svc","mall-svc","notify-svc","payment-svc","user-account-svc","wealth-charity-svc"]
  cache:
    key: ${CI_COMMIT_REF_SLUG}-${BINARY_NAME}
    paths:
      - out/
    policy: pull
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk add --no-cache openssh-client rsync jq
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_ed25519
    - echo -e "Host *\n  StrictHostKeyChecking no\n  UserKnownHostsFile /dev/null" > ~/.ssh/config
    - chmod 700 ~/.ssh && chmod 600 ~/.ssh/id_ed25519 ~/.ssh/config
    - export SERVICE_NAME="${BINARY_NAME%-svc}"
    - export DEPLOY_PATH="/data/server/supervisor/${SERVICE_NAME}"
    - mkdir -p ${DEPLOY_PATH}
    - |
      # 检查是否跳过了构建
      if [ -f "out/.skip_build" ]; then
        echo "服务 ${SERVICE_NAME} 没有变更，跳过部署"
        exit 0
      fi
      
      if [ ! -f "out/${BINARY_NAME}" ]; then
        echo "服务 ${SERVICE_NAME} 没有构建产物，跳过部署"
        exit 0
      fi
  script:
    - | 
      TIMESTAMP=$(date +%Y%m%d%H%M%S)
      echo $DEPLOY_SERVERS
      for DEPLOY_SERVER in $(echo "$DEPLOY_SERVERS" | jq -r '.[]'); do
        # 备份当前版本（如果存在）
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          pwd && \
          if [ -f ${BINARY_NAME} ]; then \
            cp ${BINARY_NAME} ${BINARY_NAME}.bak.${TIMESTAMP} && \
            echo \"备份成功: ${BINARY_NAME} -> ${BINARY_NAME}.bak.${TIMESTAMP}\"; \
          else \
            echo \"没有找到当前版本，跳过备份\"; \
          fi"
        
        # 保留最近5个备份，清理旧的
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          ls -t ${BINARY_NAME}.bak.* 2>/dev/null | tail -n +6 | xargs -r rm --"
      
        scp -P ${SSH_PORT} -o StrictHostKeyChecking=no \
          -o UserKnownHostsFile=/dev/null \
          out/${BINARY_NAME} \
          ${DEPLOY_USER}@${DEPLOY_SERVER}:${DEPLOY_PATH}/${BINARY_NAME}.latest
      
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          -o UserKnownHostsFile=/dev/null \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          cp -rf ${BINARY_NAME}.latest ${BINARY_NAME} && \
          chmod +x ${BINARY_NAME} && \
          sudo supervisorctl restart ${BINARY_NAME} && \
          echo '服务重启成功： ${BINARY_NAME}'"
      done
      

# 回滚的公共配置
.rollback_template: &rollback_template
  image: alpine:latest
  parallel:
    matrix:
      - BINARY_NAME: ["file-storage-svc","islamic-content-svc","mall-svc","notify-svc","payment-svc","user-account-svc","wealth-charity-svc"]
  variables:
    ROLLBACK_VERSION: ""
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk add --no-cache openssh-client rsync jq
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_ed25519
    - echo -e "Host *\n  StrictHostKeyChecking no\n  UserKnownHostsFile /dev/null" > ~/.ssh/config
    - chmod 700 ~/.ssh && chmod 600 ~/.ssh/id_ed25519 ~/.ssh/config
    - export SERVICE_NAME="${BINARY_NAME%-svc}"
    - export DEPLOY_PATH="/data/server/supervisor/${SERVICE_NAME}"
  script:
    - | 
      for DEPLOY_SERVER in $(echo "$DEPLOY_SERVERS" | jq -r '.[]'); do
        if [ -n "$ROLLBACK_VERSION" ]; then
          BACKUP_FILE="${BINARY_NAME}.bak.${ROLLBACK_VERSION}"
          echo "尝试回滚到指定版本: $BACKUP_FILE"
        else
          BACKUP_FILE=$(ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
            ${DEPLOY_USER}@${DEPLOY_SERVER} \
            "cd ${DEPLOY_PATH} && ls -t ${BINARY_NAME}.bak.* 2>/dev/null | head -1")
          echo "自动选择最新备份: $BACKUP_FILE"
        fi
      
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          if [ ! -f \"$BACKUP_FILE\" ]; then \
            echo \"错误: 备份文件 $BACKUP_FILE 不存在\"; \
            echo \"可用的备份文件:\"; \
            ls -la ${BINARY_NAME}.bak.* 2>/dev/null || echo \"没有找到备份文件\"; \
            exit 1; \
          fi"
      
        echo "正在回滚到: $BACKUP_FILE"
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          cp -rf \"$BACKUP_FILE\" \"${BINARY_NAME}\" && \
          chmod +x \"${BINARY_NAME}\" && \
          sudo supervisorctl restart \"${BINARY_NAME}\" && \
          echo \"回滚成功: $BACKUP_FILE -> ${BINARY_NAME}\""
      done

# 备份列表的公共配置
.baklists_template: &baklists_template
  image: alpine:latest
  parallel:
    matrix:
      - BINARY_NAME: ["file-storage-svc","islamic-content-svc","mall-svc","notify-svc","payment-svc","user-account-svc","wealth-charity-svc"]
  before_script:
    - sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
    - apk add --no-cache openssh-client rsync jq
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_ed25519
    - echo -e "Host *\n  StrictHostKeyChecking no\n  UserKnownHostsFile /dev/null" > ~/.ssh/config
    - chmod 700 ~/.ssh && chmod 600 ~/.ssh/id_ed25519 ~/.ssh/config
    - export SERVICE_NAME="${BINARY_NAME%-svc}"
    - export DEPLOY_PATH="/data/server/supervisor/${SERVICE_NAME}"
  script:
    - apk add --no-cache openssh-client jq
    - |
      for DEPLOY_SERVER in $(echo "$DEPLOY_SERVERS" | jq -r '.[]'); do
        echo "=== 服务器: $DEPLOY_SERVER 的备份列表 ==="
        ssh -p ${SSH_PORT} -o StrictHostKeyChecking=no \
          ${DEPLOY_USER}@${DEPLOY_SERVER} \
          "cd ${DEPLOY_PATH} && \
          ls -la ${BINARY_NAME}.bak.* 2>/dev/null | sort -r || echo \"没有找到备份文件\""
        echo ""
      done


# 开发环境-构建
build_dev:
  <<: *build_template
  tags:
    - dev
  only:
    - dev  # 分支
  environment:
    name: dev

# 测试环境-构建
build_fat:
  <<: *build_template
  tags:
    - fat
  only:
    - main  # 分支
  environment:
    name: fat

# 生产环境-构建（手动触发）
build_prod:
  <<: *build_template
  tags:
    - prod
  when: manual
  only:
    - prod  # 分支
  environment:
    name: prod


# 开发环境-部署
deploy_dev:
  <<: *deploy_template
  stage: deploy
  tags:
    - dev
  needs:
    - job: build_dev
      optional: true
  only:
    - dev  # 分支
  environment:
    name: dev

# 测试环境-部署
deploy_fat:
  <<: *deploy_template
  stage: deploy
  tags:
    - fat
  needs:
    - job: build_fat
      optional: true
  only:
    - main  # 分支
  environment:
    name: fat

# 生产环境-部署
deploy_prod:
  <<: *deploy_template
  stage: deploy
  tags:
    - prod
  needs:
    - job: build_prod
      optional: true
  when: manual  # 手动点击触发
  only:
    - prod  # 分支
  environment:
    name: prod


# 开发环境-手动回滚
rollback_dev:
  <<: *rollback_template
  stage: deploy
  tags:
    - dev
  when: manual
  only:
    - dev  # 分支
  environment:
    name: dev

# 测试环境-手动回滚
rollback_fat:
  <<: *rollback_template
  stage: deploy
  tags:
    - fat
  when: manual
  only:
    - main  # 分支
  environment:
    name: fat

# 生产环境-手动回滚
rollback_prod:
  <<: *rollback_template
  stage: deploy
  tags:
    - prod
  when: manual
  only:
    - prod  # 分支
  environment:
    name: prod

# 开发环境-备份列表
baklists_dev:
  <<: *baklists_template
  stage: deploy
  tags:
    - dev
  when: manual
  only:
    - dev  # 分支
  environment:
    name: dev

# 测试环境-备份列表
baklists_fat:
  <<: *baklists_template
  stage: deploy
  tags:
    - fat
  when: manual
  only:
    - main  # 分支
  environment:
    name: fat

# 生产环境-备份列表
baklists_prod:
  <<: *baklists_template
  stage: deploy
  tags:
    - prod
  when: manual
  only:
    - prod  # 分支
  environment:
    name: prod
