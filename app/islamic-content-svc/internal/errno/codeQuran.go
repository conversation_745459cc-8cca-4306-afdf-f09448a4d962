package errno

import "github.com/gogf/gf/v2/errors/gcode"

var (
	CodeRecodeNotExist = gcode.New(3001, "error.record.not.exist", nil) // 记录找不到

	// 视频相关错误码
	CodeVideoNotFound        = gcode.New(3101, "error.video.not.found", nil)        // 视频不存在
	CodeVideoNotPublished    = gcode.New(3102, "error.video.not.published", nil)    // 视频未发布
	CodePlaylistNotFound     = gcode.New(3103, "error.playlist.not.found", nil)     // 播放列表不存在
	CodeVideoCollectFailed   = gcode.New(3104, "error.video.collect.failed", nil)   // 视频收藏失败
	CodeVideoUncollectFailed = gcode.New(3105, "error.video.uncollect.failed", nil) // 视频取消收藏失败
	CodeVideoShareFailed     = gcode.New(3106, "error.video.share.failed", nil)     // 视频分享失败

	// Banner相关错误码
	CodeBannerNotFound    = gcode.New(3111, "error.banner.not.found", nil)    // Banner不存在
	CodeBannerQueryFailed = gcode.New(3112, "error.banner.query.failed", nil) // Banner查询失败
	CodeBannerStatsFailed = gcode.New(3113, "error.banner.stats.failed", nil) // Banner统计记录失败

	// Prayer相关错误码
	CodePrayerTimeNotFound    = gcode.New(3121, "error.prayer.time.not.found", nil)    // 祷告时间不存在
	CodePrayerCalculateFailed = gcode.New(3122, "error.prayer.calculate.failed", nil)  // 祷告时间计算失败
	CodePrayerDataNotFound    = gcode.New(3123, "error.prayer.data.not.found", nil)    // 祷告数据不存在
	CodePrayerQueryFailed     = gcode.New(3124, "error.prayer.query.failed", nil)      // 祷告数据查询失败
	CodeHajiJadwalNotFound    = gcode.New(3125, "error.haji.jadwal.not.found", nil)    // 朝觐日程不存在
	CodeHajiUrutanNotFound    = gcode.New(3126, "error.haji.urutan.not.found", nil)    // 朝觐仪式顺序不存在
	CodeUmrahUrutanNotFound   = gcode.New(3127, "error.umrah.urutan.not.found", nil)   // 副朝仪式顺序不存在
	CodeHajiLandmarkNotFound  = gcode.New(3128, "error.haji.landmark.not.found", nil)  // 朝觐地标不存在
	CodeUmrahLandmarkNotFound = gcode.New(3129, "error.umrah.landmark.not.found", nil) // 副朝地标不存在
	CodeDoaDataNotFound       = gcode.New(3130, "error.doa.data.not.found", nil)       // 祈祷文数据不存在
)
