// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// userCategoryRelationsDao is the data access object for the table user_category_relations.
// You can define custom methods on it to extend its functionality as needed.
type userCategoryRelationsDao struct {
	*internal.UserCategoryRelationsDao
}

var (
	// UserCategoryRelations is a globally accessible object for table user_category_relations operations.
	UserCategoryRelations = userCategoryRelationsDao{internal.NewUserCategoryRelationsDao()}
)

// Add your custom methods and functionality below.
