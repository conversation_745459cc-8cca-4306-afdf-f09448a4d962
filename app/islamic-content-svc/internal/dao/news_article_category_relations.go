// =================================================================================
// This file is auto-generated by the GoFrame CLI tool. You may modify it as needed.
// =================================================================================

package dao

import (
	"halalplus/app/islamic-content-svc/internal/dao/internal"
)

// newsArticleCategoryRelationsDao is the data access object for the table news_article_category_relations.
// You can define custom methods on it to extend its functionality as needed.
type newsArticleCategoryRelationsDao struct {
	*internal.NewsArticleCategoryRelationsDao
}

var (
	// NewsArticleCategoryRelations is a globally accessible object for table news_article_category_relations operations.
	NewsArticleCategoryRelations = newsArticleCategoryRelationsDao{internal.NewNewsArticleCategoryRelationsDao()}
)

// Add your custom methods and functionality below.
