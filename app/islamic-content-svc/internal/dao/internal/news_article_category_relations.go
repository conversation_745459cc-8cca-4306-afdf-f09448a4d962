// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleCategoryRelationsDao is the data access object for the table news_article_category_relations.
type NewsArticleCategoryRelationsDao struct {
	table    string                              // table is the underlying table name of the DAO.
	group    string                              // group is the database configuration group name of the current DAO.
	columns  NewsArticleCategoryRelationsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler                  // handlers for customized model modification.
}

// NewsArticleCategoryRelationsColumns defines and stores column names for the table news_article_category_relations.
type NewsArticleCategoryRelationsColumns struct {
	Id         string //
	ArticleId  string // 文章id
	CategoryId string // 分类id
}

// newsArticleCategoryRelationsColumns holds the columns for the table news_article_category_relations.
var newsArticleCategoryRelationsColumns = NewsArticleCategoryRelationsColumns{
	Id:         "id",
	ArticleId:  "article_id",
	CategoryId: "category_id",
}

// NewNewsArticleCategoryRelationsDao creates and returns a new DAO object for table data access.
func NewNewsArticleCategoryRelationsDao(handlers ...gdb.ModelHandler) *NewsArticleCategoryRelationsDao {
	return &NewsArticleCategoryRelationsDao{
		group:    "default",
		table:    "news_article_category_relations",
		columns:  newsArticleCategoryRelationsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *NewsArticleCategoryRelationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *NewsArticleCategoryRelationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *NewsArticleCategoryRelationsDao) Columns() NewsArticleCategoryRelationsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *NewsArticleCategoryRelationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *NewsArticleCategoryRelationsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *NewsArticleCategoryRelationsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
