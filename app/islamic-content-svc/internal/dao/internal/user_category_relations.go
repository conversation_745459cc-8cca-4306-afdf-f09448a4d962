// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

package internal

import (
	"context"

	"github.com/gogf/gf/v2/database/gdb"
	"github.com/gogf/gf/v2/frame/g"
)

// UserCategoryRelationsDao is the data access object for the table user_category_relations.
type UserCategoryRelationsDao struct {
	table    string                       // table is the underlying table name of the DAO.
	group    string                       // group is the database configuration group name of the current DAO.
	columns  UserCategoryRelationsColumns // columns contains all the column names of Table for convenient usage.
	handlers []gdb.ModelHandler           // handlers for customized model modification.
}

// UserCategoryRelationsColumns defines and stores column names for the table user_category_relations.
type UserCategoryRelationsColumns struct {
	Id         string //
	UserId     string // user id
	CategoryId string // 分类id
}

// userCategoryRelationsColumns holds the columns for the table user_category_relations.
var userCategoryRelationsColumns = UserCategoryRelationsColumns{
	Id:         "id",
	UserId:     "user_id",
	CategoryId: "category_id",
}

// NewUserCategoryRelationsDao creates and returns a new DAO object for table data access.
func NewUserCategoryRelationsDao(handlers ...gdb.ModelHandler) *UserCategoryRelationsDao {
	return &UserCategoryRelationsDao{
		group:    "default",
		table:    "user_category_relations",
		columns:  userCategoryRelationsColumns,
		handlers: handlers,
	}
}

// DB retrieves and returns the underlying raw database management object of the current DAO.
func (dao *UserCategoryRelationsDao) DB() gdb.DB {
	return g.DB(dao.group)
}

// Table returns the table name of the current DAO.
func (dao *UserCategoryRelationsDao) Table() string {
	return dao.table
}

// Columns returns all column names of the current DAO.
func (dao *UserCategoryRelationsDao) Columns() UserCategoryRelationsColumns {
	return dao.columns
}

// Group returns the database configuration group name of the current DAO.
func (dao *UserCategoryRelationsDao) Group() string {
	return dao.group
}

// Ctx creates and returns a Model for the current DAO. It automatically sets the context for the current operation.
func (dao *UserCategoryRelationsDao) Ctx(ctx context.Context) *gdb.Model {
	model := dao.DB().Model(dao.table)
	for _, handler := range dao.handlers {
		model = handler(model)
	}
	return model.Safe().Ctx(ctx)
}

// Transaction wraps the transaction logic using function f.
// It rolls back the transaction and returns the error if function f returns a non-nil error.
// It commits the transaction and returns nil if function f returns nil.
//
// Note: Do not commit or roll back the transaction in function f,
// as it is automatically handled by this function.
func (dao *UserCategoryRelationsDao) Transaction(ctx context.Context, f func(ctx context.Context, tx gdb.TX) error) (err error) {
	return dao.Ctx(ctx).Transaction(ctx, f)
}
