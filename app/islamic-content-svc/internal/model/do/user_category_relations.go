// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// UserCategoryRelations is the golang structure of table user_category_relations for DAO operations like Where/Data.
type UserCategoryRelations struct {
	g.Meta     `orm:"table:user_category_relations, do:true"`
	Id         interface{} //
	UserId     interface{} // user id
	CategoryId interface{} // 分类id
}
