// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package do

import (
	"github.com/gogf/gf/v2/frame/g"
)

// NewsArticleCategoryRelations is the golang structure of table news_article_category_relations for DAO operations like Where/Data.
type NewsArticleCategoryRelations struct {
	g.Meta     `orm:"table:news_article_category_relations, do:true"`
	Id         interface{} //
	ArticleId  interface{} // 文章id
	CategoryId interface{} // 分类id
}
