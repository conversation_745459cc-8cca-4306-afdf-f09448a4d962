// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// NewsArticleCategoryRelations is the golang structure for table news_article_category_relations.
type NewsArticleCategoryRelations struct {
	Id         uint `json:"id"         orm:"id"          description:""`     //
	ArticleId  uint `json:"articleId"  orm:"article_id"  description:"文章id"` // 文章id
	CategoryId uint `json:"categoryId" orm:"category_id" description:"分类id"` // 分类id
}
