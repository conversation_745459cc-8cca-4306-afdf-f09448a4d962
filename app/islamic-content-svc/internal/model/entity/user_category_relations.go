// =================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// =================================================================================

package entity

// UserCategoryRelations is the golang structure for table user_category_relations.
type UserCategoryRelations struct {
	Id         uint `json:"id"         orm:"id"          description:""`        //
	UserId     uint `json:"userId"     orm:"user_id"     description:"user id"` // user id
	CategoryId uint `json:"categoryId" orm:"category_id" description:"分类id"`    // 分类id
}
