// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: islamic/v1/news.proto

package islamicv1

import (
	common "halalplus/api/common"
	_ "halalplus/app/islamic-content-svc/api/pbentity"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/wrapperspb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UserNewsCategoryEditReq struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// repeated int32 ids = 1; // cate_ids 是 int32 数组
	Ids           string   `protobuf:"bytes,1,opt,name=ids,proto3" json:"ids,omitempty" dc:"repeated int32 ids = 1; // cate_ids 是 int32 数组ids 是字符串，逗号分隔"` // ids 是字符串，逗号分隔
	Name          string   `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" dc:"juz名称"`                                                     // juz名称
	Ppid          []uint32 `protobuf:"varint,3,rep,packed,name=ppid,proto3" json:"ppid,omitempty"`
	Message       string   `protobuf:"bytes,4,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNewsCategoryEditReq) Reset() {
	*x = UserNewsCategoryEditReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNewsCategoryEditReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNewsCategoryEditReq) ProtoMessage() {}

func (x *UserNewsCategoryEditReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNewsCategoryEditReq.ProtoReflect.Descriptor instead.
func (*UserNewsCategoryEditReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{0}
}

func (x *UserNewsCategoryEditReq) GetIds() string {
	if x != nil {
		return x.Ids
	}
	return ""
}

func (x *UserNewsCategoryEditReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserNewsCategoryEditReq) GetPpid() []uint32 {
	if x != nil {
		return x.Ppid
	}
	return nil
}

func (x *UserNewsCategoryEditReq) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type UserNewsCategoryEditRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNewsCategoryEditRes) Reset() {
	*x = UserNewsCategoryEditRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNewsCategoryEditRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNewsCategoryEditRes) ProtoMessage() {}

func (x *UserNewsCategoryEditRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNewsCategoryEditRes.ProtoReflect.Descriptor instead.
func (*UserNewsCategoryEditRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{1}
}

func (x *UserNewsCategoryEditRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserNewsCategoryEditRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserNewsCategoryEditRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type UserNewsCategoryListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           uint32                 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty" dc:"父类id"` //父类id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNewsCategoryListReq) Reset() {
	*x = UserNewsCategoryListReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNewsCategoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNewsCategoryListReq) ProtoMessage() {}

func (x *UserNewsCategoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNewsCategoryListReq.ProtoReflect.Descriptor instead.
func (*UserNewsCategoryListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{2}
}

func (x *UserNewsCategoryListReq) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type UserNewsCategoryListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UnSelectList  []*CategoryInfo        `protobuf:"bytes,1,rep,name=unSelectList,proto3" json:"unSelectList,omitempty"`
	SelectList    []*CategoryInfo        `protobuf:"bytes,2,rep,name=selectList,proto3" json:"selectList,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNewsCategoryListResData) Reset() {
	*x = UserNewsCategoryListResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNewsCategoryListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNewsCategoryListResData) ProtoMessage() {}

func (x *UserNewsCategoryListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNewsCategoryListResData.ProtoReflect.Descriptor instead.
func (*UserNewsCategoryListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{3}
}

func (x *UserNewsCategoryListResData) GetUnSelectList() []*CategoryInfo {
	if x != nil {
		return x.UnSelectList
	}
	return nil
}

func (x *UserNewsCategoryListResData) GetSelectList() []*CategoryInfo {
	if x != nil {
		return x.SelectList
	}
	return nil
}

type UserNewsCategoryListRes struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	Code          int32                        `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                       `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error                `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserNewsCategoryListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserNewsCategoryListRes) Reset() {
	*x = UserNewsCategoryListRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserNewsCategoryListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserNewsCategoryListRes) ProtoMessage() {}

func (x *UserNewsCategoryListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserNewsCategoryListRes.ProtoReflect.Descriptor instead.
func (*UserNewsCategoryListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{4}
}

func (x *UserNewsCategoryListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserNewsCategoryListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserNewsCategoryListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserNewsCategoryListRes) GetData() *UserNewsCategoryListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCategoryListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pid           uint32                 `protobuf:"varint,1,opt,name=pid,proto3" json:"pid,omitempty" dc:"父类id"` //父类id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCategoryListReq) Reset() {
	*x = NewsCategoryListReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCategoryListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListReq) ProtoMessage() {}

func (x *NewsCategoryListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListReq.ProtoReflect.Descriptor instead.
func (*NewsCategoryListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{5}
}

func (x *NewsCategoryListReq) GetPid() uint32 {
	if x != nil {
		return x.Pid
	}
	return 0
}

type CategoryInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	ParentId      uint32                 `protobuf:"varint,2,opt,name=parent_id,json=parentId,proto3" json:"parent_id,omitempty"`
	LanguageId    uint32                 `protobuf:"varint,3,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	CoverImgs     string                 `protobuf:"bytes,5,opt,name=cover_imgs,json=coverImgs,proto3" json:"cover_imgs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CategoryInfo) Reset() {
	*x = CategoryInfo{}
	mi := &file_islamic_v1_news_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CategoryInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CategoryInfo) ProtoMessage() {}

func (x *CategoryInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CategoryInfo.ProtoReflect.Descriptor instead.
func (*CategoryInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{6}
}

func (x *CategoryInfo) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CategoryInfo) GetParentId() uint32 {
	if x != nil {
		return x.ParentId
	}
	return 0
}

func (x *CategoryInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *CategoryInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CategoryInfo) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

type NewsCategoryListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*CategoryInfo        `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCategoryListResData) Reset() {
	*x = NewsCategoryListResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCategoryListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListResData) ProtoMessage() {}

func (x *NewsCategoryListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListResData.ProtoReflect.Descriptor instead.
func (*NewsCategoryListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{7}
}

func (x *NewsCategoryListResData) GetList() []*CategoryInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsCategoryListRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsCategoryListResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCategoryListRes) Reset() {
	*x = NewsCategoryListRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCategoryListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCategoryListRes) ProtoMessage() {}

func (x *NewsCategoryListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCategoryListRes.ProtoReflect.Descriptor instead.
func (*NewsCategoryListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{8}
}

func (x *NewsCategoryListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCategoryListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCategoryListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCategoryListRes) GetData() *NewsCategoryListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsListByCateIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CateId        int32                  `protobuf:"varint,1,opt,name=cate_id,json=cateId,proto3" json:"cate_id,omitempty" dc:"cate_id 是 int32"`                       // cate_id 是 int32
	IsRecommend   uint32                 `protobuf:"varint,2,opt,name=is_recommend,json=isRecommend,proto3" json:"is_recommend,omitempty" dc:"是否推荐,0-否,1-是"`           // 是否推荐,0-否,1-是
	ArticleName   string                 `protobuf:"bytes,3,opt,name=article_name,json=articleName,proto3" json:"article_name,omitempty" dc:"文章标题"`                    // 文章标题
	Page          *common.PageRequest    `protobuf:"bytes,4,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                                                     // 分页参数
	IsTop         uint32                 `protobuf:"varint,5,opt,name=is_top,json=isTop,proto3" json:"is_top,omitempty" dc:"是否头条,0-否,1-是"`                             // 是否头条,0-否,1-是
	CateIds       []int32                `protobuf:"varint,6,rep,packed,name=cate_ids,json=cateIds,proto3" json:"cate_ids,omitempty" dc:"cate_ids 是 int32 数组"`         // cate_ids 是 int32 数组
	ExcludeId     int32                  `protobuf:"varint,7,opt,name=exclude_id,json=excludeId,proto3" json:"exclude_id,omitempty" dc:"exclude_id 排除id 关联推荐内容需要排除本身"` // exclude_id 排除id 关联推荐内容需要排除本身
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByCateIdReq) Reset() {
	*x = NewsListByCateIdReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByCateIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdReq) ProtoMessage() {}

func (x *NewsListByCateIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdReq.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{9}
}

func (x *NewsListByCateIdReq) GetCateId() int32 {
	if x != nil {
		return x.CateId
	}
	return 0
}

func (x *NewsListByCateIdReq) GetIsRecommend() uint32 {
	if x != nil {
		return x.IsRecommend
	}
	return 0
}

func (x *NewsListByCateIdReq) GetArticleName() string {
	if x != nil {
		return x.ArticleName
	}
	return ""
}

func (x *NewsListByCateIdReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *NewsListByCateIdReq) GetIsTop() uint32 {
	if x != nil {
		return x.IsTop
	}
	return 0
}

func (x *NewsListByCateIdReq) GetCateIds() []int32 {
	if x != nil {
		return x.CateIds
	}
	return nil
}

func (x *NewsListByCateIdReq) GetExcludeId() int32 {
	if x != nil {
		return x.ExcludeId
	}
	return 0
}

type NewsListByCateIdResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ArticleInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByCateIdResData) Reset() {
	*x = NewsListByCateIdResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByCateIdResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdResData) ProtoMessage() {}

func (x *NewsListByCateIdResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdResData.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{10}
}

func (x *NewsListByCateIdResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *NewsListByCateIdResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsListByCateIdRes struct {
	state         protoimpl.MessageState   `protogen:"open.v1"`
	Code          int32                    `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                   `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error            `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsListByCateIdResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByCateIdRes) Reset() {
	*x = NewsListByCateIdRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByCateIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByCateIdRes) ProtoMessage() {}

func (x *NewsListByCateIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByCateIdRes.ProtoReflect.Descriptor instead.
func (*NewsListByCateIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{11}
}

func (x *NewsListByCateIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsListByCateIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsListByCateIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsListByCateIdRes) GetData() *NewsListByCateIdResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsHotListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsHot         uint32                 `protobuf:"varint,1,opt,name=is_hot,json=isHot,proto3" json:"is_hot,omitempty" dc:"是否热门3条数据 0-否,1-是"` // 是否热门3条数据 0-否,1-是
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                             // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsHotListReq) Reset() {
	*x = NewsHotListReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsHotListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListReq) ProtoMessage() {}

func (x *NewsHotListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListReq.ProtoReflect.Descriptor instead.
func (*NewsHotListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{12}
}

func (x *NewsHotListReq) GetIsHot() uint32 {
	if x != nil {
		return x.IsHot
	}
	return 0
}

func (x *NewsHotListReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsHotListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ArticleInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsHotListResData) Reset() {
	*x = NewsHotListResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsHotListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListResData) ProtoMessage() {}

func (x *NewsHotListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListResData.ProtoReflect.Descriptor instead.
func (*NewsHotListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{13}
}

func (x *NewsHotListResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *NewsHotListResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsHotListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsHotListResData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsHotListRes) Reset() {
	*x = NewsHotListRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsHotListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsHotListRes) ProtoMessage() {}

func (x *NewsHotListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsHotListRes.ProtoReflect.Descriptor instead.
func (*NewsHotListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{14}
}

func (x *NewsHotListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsHotListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsHotListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsHotListRes) GetData() *NewsHotListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsInfoReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     int32                  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsInfoReq) Reset() {
	*x = NewsInfoReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsInfoReq) ProtoMessage() {}

func (x *NewsInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsInfoReq.ProtoReflect.Descriptor instead.
func (*NewsInfoReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{15}
}

func (x *NewsInfoReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsInfoRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *ArticleInfo           `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsInfoRes) Reset() {
	*x = NewsInfoRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsInfoRes) ProtoMessage() {}

func (x *NewsInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsInfoRes.ProtoReflect.Descriptor instead.
func (*NewsInfoRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{16}
}

func (x *NewsInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsInfoRes) GetData() *ArticleInfo {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCollectReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          *common.PageRequest    `protobuf:"bytes,1,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectReq) Reset() {
	*x = NewsCollectReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectReq) ProtoMessage() {}

func (x *NewsCollectReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectReq.ProtoReflect.Descriptor instead.
func (*NewsCollectReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{17}
}

func (x *NewsCollectReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsCollectResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ArticleInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectResData) Reset() {
	*x = NewsCollectResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectResData) ProtoMessage() {}

func (x *NewsCollectResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectResData.ProtoReflect.Descriptor instead.
func (*NewsCollectResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{18}
}

func (x *NewsCollectResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *NewsCollectResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

type NewsCollectRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsCollectResData    `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectRes) Reset() {
	*x = NewsCollectRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectRes) ProtoMessage() {}

func (x *NewsCollectRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectRes.ProtoReflect.Descriptor instead.
func (*NewsCollectRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{19}
}

func (x *NewsCollectRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCollectRes) GetData() *NewsCollectResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCollectStatusCheckReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     int32                  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectStatusCheckReq) Reset() {
	*x = NewsCollectStatusCheckReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectStatusCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectStatusCheckReq) ProtoMessage() {}

func (x *NewsCollectStatusCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectStatusCheckReq.ProtoReflect.Descriptor instead.
func (*NewsCollectStatusCheckReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{20}
}

func (x *NewsCollectStatusCheckReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsCollectStatusCheckData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IsCollect     int32                  `protobuf:"varint,1,opt,name=is_collect,json=isCollect,proto3" json:"is_collect,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectStatusCheckData) Reset() {
	*x = NewsCollectStatusCheckData{}
	mi := &file_islamic_v1_news_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectStatusCheckData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectStatusCheckData) ProtoMessage() {}

func (x *NewsCollectStatusCheckData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectStatusCheckData.ProtoReflect.Descriptor instead.
func (*NewsCollectStatusCheckData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{21}
}

func (x *NewsCollectStatusCheckData) GetIsCollect() int32 {
	if x != nil {
		return x.IsCollect
	}
	return 0
}

type NewsCollectStatusCheckRes struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Code          int32                       `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                      `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error               `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsCollectStatusCheckData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectStatusCheckRes) Reset() {
	*x = NewsCollectStatusCheckRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectStatusCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectStatusCheckRes) ProtoMessage() {}

func (x *NewsCollectStatusCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectStatusCheckRes.ProtoReflect.Descriptor instead.
func (*NewsCollectStatusCheckRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{22}
}

func (x *NewsCollectStatusCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectStatusCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectStatusCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsCollectStatusCheckRes) GetData() *NewsCollectStatusCheckData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsCollectOpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     int32                  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectOpReq) Reset() {
	*x = NewsCollectOpReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectOpReq) ProtoMessage() {}

func (x *NewsCollectOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectOpReq.ProtoReflect.Descriptor instead.
func (*NewsCollectOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{23}
}

func (x *NewsCollectOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsCollectOpRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsCollectOpRes) Reset() {
	*x = NewsCollectOpRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsCollectOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsCollectOpRes) ProtoMessage() {}

func (x *NewsCollectOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsCollectOpRes.ProtoReflect.Descriptor instead.
func (*NewsCollectOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{24}
}

func (x *NewsCollectOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsCollectOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsCollectOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsShareOpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     int32                  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsShareOpReq) Reset() {
	*x = NewsShareOpReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsShareOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsShareOpReq) ProtoMessage() {}

func (x *NewsShareOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsShareOpReq.ProtoReflect.Descriptor instead.
func (*NewsShareOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{25}
}

func (x *NewsShareOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsShareOpRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsShareOpRes) Reset() {
	*x = NewsShareOpRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsShareOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsShareOpRes) ProtoMessage() {}

func (x *NewsShareOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsShareOpRes.ProtoReflect.Descriptor instead.
func (*NewsShareOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{26}
}

func (x *NewsShareOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsShareOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsShareOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsViewOpReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArticleId     int32                  `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsViewOpReq) Reset() {
	*x = NewsViewOpReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsViewOpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsViewOpReq) ProtoMessage() {}

func (x *NewsViewOpReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsViewOpReq.ProtoReflect.Descriptor instead.
func (*NewsViewOpReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{27}
}

func (x *NewsViewOpReq) GetArticleId() int32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

type NewsViewOpRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsViewOpRes) Reset() {
	*x = NewsViewOpRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsViewOpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsViewOpRes) ProtoMessage() {}

func (x *NewsViewOpRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsViewOpRes.ProtoReflect.Descriptor instead.
func (*NewsViewOpRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{28}
}

func (x *NewsViewOpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsViewOpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsViewOpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type NewsTopicListReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsTopicListReq) Reset() {
	*x = NewsTopicListReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsTopicListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListReq) ProtoMessage() {}

func (x *NewsTopicListReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListReq.ProtoReflect.Descriptor instead.
func (*NewsTopicListReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{29}
}

type TopicInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TopicId       uint32                 `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" dc:"用户id"`          // 用户id
	LanguageId    uint32                 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"用户id"` // 用户id
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"账号"`                                  // 账号
	ShortName     string                 `protobuf:"bytes,4,opt,name=short_name,json=shortName,proto3" json:"short_name,omitempty" dc:"账号"`       // 账号
	TopicImgs     string                 `protobuf:"bytes,5,opt,name=topic_imgs,json=topicImgs,proto3" json:"topic_imgs,omitempty" dc:"专题图片"`     // 专题图片
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TopicInfo) Reset() {
	*x = TopicInfo{}
	mi := &file_islamic_v1_news_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TopicInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TopicInfo) ProtoMessage() {}

func (x *TopicInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TopicInfo.ProtoReflect.Descriptor instead.
func (*TopicInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{30}
}

func (x *TopicInfo) GetTopicId() uint32 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *TopicInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *TopicInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TopicInfo) GetShortName() string {
	if x != nil {
		return x.ShortName
	}
	return ""
}

func (x *TopicInfo) GetTopicImgs() string {
	if x != nil {
		return x.TopicImgs
	}
	return ""
}

type NewsTopicListResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*TopicInfo           `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsTopicListResData) Reset() {
	*x = NewsTopicListResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsTopicListResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListResData) ProtoMessage() {}

func (x *NewsTopicListResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListResData.ProtoReflect.Descriptor instead.
func (*NewsTopicListResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{31}
}

func (x *NewsTopicListResData) GetList() []*TopicInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type NewsTopicListRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsTopicListResData  `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty" dc:"专题信息"` // 专题信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsTopicListRes) Reset() {
	*x = NewsTopicListRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsTopicListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsTopicListRes) ProtoMessage() {}

func (x *NewsTopicListRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsTopicListRes.ProtoReflect.Descriptor instead.
func (*NewsTopicListRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{32}
}

func (x *NewsTopicListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsTopicListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsTopicListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsTopicListRes) GetData() *NewsTopicListResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type NewsListByTopicIdReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TopicId       uint32                 `protobuf:"varint,1,opt,name=topic_id,json=topicId,proto3" json:"topic_id,omitempty" dc:"话题id"` // 话题id
	Page          *common.PageRequest    `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"`                       // 分页参数
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByTopicIdReq) Reset() {
	*x = NewsListByTopicIdReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByTopicIdReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdReq) ProtoMessage() {}

func (x *NewsListByTopicIdReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdReq.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{33}
}

func (x *NewsListByTopicIdReq) GetTopicId() uint32 {
	if x != nil {
		return x.TopicId
	}
	return 0
}

func (x *NewsListByTopicIdReq) GetPage() *common.PageRequest {
	if x != nil {
		return x.Page
	}
	return nil
}

type ArticleInfo struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ArticleId        uint32                 `protobuf:"varint,1,opt,name=article_id,json=articleId,proto3" json:"article_id,omitempty" dc:"文章id"`                         // 文章id
	LanguageId       uint32                 `protobuf:"varint,2,opt,name=language_id,json=languageId,proto3" json:"language_id,omitempty" dc:"语言id"`                      // 语言id
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty" dc:"文章标题"`                                                     // 文章标题
	Content          string                 `protobuf:"bytes,4,opt,name=content,proto3" json:"content,omitempty" dc:"文章内容"`                                               // 文章内容
	CategoryId       uint32                 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty" dc:"分类id"`                      // 分类id
	CategoryName     string                 `protobuf:"bytes,6,opt,name=category_name,json=categoryName,proto3" json:"category_name,omitempty" dc:"分类名称"`                 // 分类名称
	CoverImgs        string                 `protobuf:"bytes,7,opt,name=cover_imgs,json=coverImgs,proto3" json:"cover_imgs,omitempty" dc:"专题图片"`                          // 专题图片
	Author           string                 `protobuf:"bytes,8,opt,name=author,proto3" json:"author,omitempty" dc:"创建人"`                                                  // 创建人
	PublishTime      int64                  `protobuf:"varint,9,opt,name=publish_time,json=publishTime,proto3" json:"publish_time,omitempty" dc:"发布时间"`                   // 发布时间
	AuthorLogo       string                 `protobuf:"bytes,10,opt,name=author_logo,json=authorLogo,proto3" json:"author_logo,omitempty" dc:"发布时间"`                      // 发布时间
	AuthorAuthStatus uint32                 `protobuf:"varint,11,opt,name=author_auth_status,json=authorAuthStatus,proto3" json:"author_auth_status,omitempty" dc:"发布时间"` // 发布时间
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ArticleInfo) Reset() {
	*x = ArticleInfo{}
	mi := &file_islamic_v1_news_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ArticleInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ArticleInfo) ProtoMessage() {}

func (x *ArticleInfo) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ArticleInfo.ProtoReflect.Descriptor instead.
func (*ArticleInfo) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{34}
}

func (x *ArticleInfo) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *ArticleInfo) GetLanguageId() uint32 {
	if x != nil {
		return x.LanguageId
	}
	return 0
}

func (x *ArticleInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ArticleInfo) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *ArticleInfo) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ArticleInfo) GetCategoryName() string {
	if x != nil {
		return x.CategoryName
	}
	return ""
}

func (x *ArticleInfo) GetCoverImgs() string {
	if x != nil {
		return x.CoverImgs
	}
	return ""
}

func (x *ArticleInfo) GetAuthor() string {
	if x != nil {
		return x.Author
	}
	return ""
}

func (x *ArticleInfo) GetPublishTime() int64 {
	if x != nil {
		return x.PublishTime
	}
	return 0
}

func (x *ArticleInfo) GetAuthorLogo() string {
	if x != nil {
		return x.AuthorLogo
	}
	return ""
}

func (x *ArticleInfo) GetAuthorAuthStatus() uint32 {
	if x != nil {
		return x.AuthorAuthStatus
	}
	return 0
}

type NewsListByTopicIdResData struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	List          []*ArticleInfo         `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
	Page          *common.PageResponse   `protobuf:"bytes,2,opt,name=page,proto3" json:"page,omitempty" dc:"分页参数"` // 分页参数
	TopicName     string                 `protobuf:"bytes,3,opt,name=topic_name,json=topicName,proto3" json:"topic_name,omitempty"`
	TopicImg      string                 `protobuf:"bytes,4,opt,name=topic_img,json=topicImg,proto3" json:"topic_img,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByTopicIdResData) Reset() {
	*x = NewsListByTopicIdResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByTopicIdResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdResData) ProtoMessage() {}

func (x *NewsListByTopicIdResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdResData.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{35}
}

func (x *NewsListByTopicIdResData) GetList() []*ArticleInfo {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *NewsListByTopicIdResData) GetPage() *common.PageResponse {
	if x != nil {
		return x.Page
	}
	return nil
}

func (x *NewsListByTopicIdResData) GetTopicName() string {
	if x != nil {
		return x.TopicName
	}
	return ""
}

func (x *NewsListByTopicIdResData) GetTopicImg() string {
	if x != nil {
		return x.TopicImg
	}
	return ""
}

type NewsListByTopicIdRes struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Code          int32                     `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                    `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error             `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *NewsListByTopicIdResData `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsListByTopicIdRes) Reset() {
	*x = NewsListByTopicIdRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsListByTopicIdRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsListByTopicIdRes) ProtoMessage() {}

func (x *NewsListByTopicIdRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsListByTopicIdRes.ProtoReflect.Descriptor instead.
func (*NewsListByTopicIdRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{36}
}

func (x *NewsListByTopicIdRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *NewsListByTopicIdRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *NewsListByTopicIdRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *NewsListByTopicIdRes) GetData() *NewsListByTopicIdResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserBookmarkReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBookmarkReq) Reset() {
	*x = UserBookmarkReq{}
	mi := &file_islamic_v1_news_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBookmarkReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBookmarkReq) ProtoMessage() {}

func (x *UserBookmarkReq) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBookmarkReq.ProtoReflect.Descriptor instead.
func (*UserBookmarkReq) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{37}
}

type UserBookmarkResData struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	AyahCounts     uint32                 `protobuf:"varint,1,opt,name=ayah_counts,json=ayahCounts,proto3" json:"ayah_counts,omitempty" dc:"ayah数量"`                 // ayah数量
	DoaCounts      uint32                 `protobuf:"varint,2,opt,name=doa_counts,json=doaCounts,proto3" json:"doa_counts,omitempty" dc:"doa数量"`                     // doa数量
	ActivityCounts uint32                 `protobuf:"varint,3,opt,name=activity_counts,json=activityCounts,proto3" json:"activity_counts,omitempty" dc:"activity数量"` // activity数量
	ArticleCounts  uint32                 `protobuf:"varint,4,opt,name=article_counts,json=articleCounts,proto3" json:"article_counts,omitempty" dc:"article数量"`     // article数量
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UserBookmarkResData) Reset() {
	*x = UserBookmarkResData{}
	mi := &file_islamic_v1_news_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBookmarkResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBookmarkResData) ProtoMessage() {}

func (x *UserBookmarkResData) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBookmarkResData.ProtoReflect.Descriptor instead.
func (*UserBookmarkResData) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{38}
}

func (x *UserBookmarkResData) GetAyahCounts() uint32 {
	if x != nil {
		return x.AyahCounts
	}
	return 0
}

func (x *UserBookmarkResData) GetDoaCounts() uint32 {
	if x != nil {
		return x.DoaCounts
	}
	return 0
}

func (x *UserBookmarkResData) GetActivityCounts() uint32 {
	if x != nil {
		return x.ActivityCounts
	}
	return 0
}

func (x *UserBookmarkResData) GetArticleCounts() uint32 {
	if x != nil {
		return x.ArticleCounts
	}
	return 0
}

type UserBookmarkRes struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          int32                  `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg           string                 `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error         *common.Error          `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data          *UserBookmarkResData   `protobuf:"bytes,5,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserBookmarkRes) Reset() {
	*x = UserBookmarkRes{}
	mi := &file_islamic_v1_news_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserBookmarkRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserBookmarkRes) ProtoMessage() {}

func (x *UserBookmarkRes) ProtoReflect() protoreflect.Message {
	mi := &file_islamic_v1_news_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserBookmarkRes.ProtoReflect.Descriptor instead.
func (*UserBookmarkRes) Descriptor() ([]byte, []int) {
	return file_islamic_v1_news_proto_rawDescGZIP(), []int{39}
}

func (x *UserBookmarkRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserBookmarkRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserBookmarkRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserBookmarkRes) GetData() *UserBookmarkResData {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_islamic_v1_news_proto protoreflect.FileDescriptor

const file_islamic_v1_news_proto_rawDesc = "" +
	"\n" +
	"\x15islamic/v1/news.proto\x12\n" +
	"islamic.v1\x1a\x1bpbentity/news_article.proto\x1a$pbentity/news_article_language.proto\x1a\x1cpbentity/news_category.proto\x1a%pbentity/news_category_language.proto\x1a\x19pbentity/news_topic.proto\x1a\"pbentity/news_topic_language.proto\x1a\x17common/front_info.proto\x1a\x11common/base.proto\x1a\x1egoogle/protobuf/wrappers.proto\"m\n" +
	"\x17UserNewsCategoryEditReq\x12\x10\n" +
	"\x03ids\x18\x01 \x01(\tR\x03ids\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04ppid\x18\x03 \x03(\rR\x04ppid\x12\x18\n" +
	"\amessage\x18\x04 \x01(\tR\amessage\"d\n" +
	"\x17UserNewsCategoryEditRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"+\n" +
	"\x17UserNewsCategoryListReq\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\rR\x03pid\"\x95\x01\n" +
	"\x1bUserNewsCategoryListResData\x12<\n" +
	"\funSelectList\x18\x01 \x03(\v2\x18.islamic.v1.CategoryInfoR\funSelectList\x128\n" +
	"\n" +
	"selectList\x18\x02 \x03(\v2\x18.islamic.v1.CategoryInfoR\n" +
	"selectList\"\xa1\x01\n" +
	"\x17UserNewsCategoryListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12;\n" +
	"\x04data\x18\x04 \x01(\v2'.islamic.v1.UserNewsCategoryListResDataR\x04data\"'\n" +
	"\x13NewsCategoryListReq\x12\x10\n" +
	"\x03pid\x18\x01 \x01(\rR\x03pid\"\x8f\x01\n" +
	"\fCategoryInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\rR\x02id\x12\x1b\n" +
	"\tparent_id\x18\x02 \x01(\rR\bparentId\x12\x1f\n" +
	"\vlanguage_id\x18\x03 \x01(\rR\n" +
	"languageId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"cover_imgs\x18\x05 \x01(\tR\tcoverImgs\"G\n" +
	"\x17NewsCategoryListResData\x12,\n" +
	"\x04list\x18\x01 \x03(\v2\x18.islamic.v1.CategoryInfoR\x04list\"\x99\x01\n" +
	"\x13NewsCategoryListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.NewsCategoryListResDataR\x04data\"\xee\x01\n" +
	"\x13NewsListByCateIdReq\x12\x17\n" +
	"\acate_id\x18\x01 \x01(\x05R\x06cateId\x12!\n" +
	"\fis_recommend\x18\x02 \x01(\rR\visRecommend\x12!\n" +
	"\farticle_name\x18\x03 \x01(\tR\varticleName\x12'\n" +
	"\x04page\x18\x04 \x01(\v2\x13.common.PageRequestR\x04page\x12\x15\n" +
	"\x06is_top\x18\x05 \x01(\rR\x05isTop\x12\x19\n" +
	"\bcate_ids\x18\x06 \x03(\x05R\acateIds\x12\x1d\n" +
	"\n" +
	"exclude_id\x18\a \x01(\x05R\texcludeId\"p\n" +
	"\x17NewsListByCateIdResData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.islamic.v1.ArticleInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x99\x01\n" +
	"\x13NewsListByCateIdRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x127\n" +
	"\x04data\x18\x04 \x01(\v2#.islamic.v1.NewsListByCateIdResDataR\x04data\"P\n" +
	"\x0eNewsHotListReq\x12\x15\n" +
	"\x06is_hot\x18\x01 \x01(\rR\x05isHot\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"k\n" +
	"\x12NewsHotListResData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.islamic.v1.ArticleInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8f\x01\n" +
	"\x0eNewsHotListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.NewsHotListResDataR\x04data\",\n" +
	"\vNewsInfoReq\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x05R\tarticleId\"\x85\x01\n" +
	"\vNewsInfoRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12+\n" +
	"\x04data\x18\x04 \x01(\v2\x17.islamic.v1.ArticleInfoR\x04data\"9\n" +
	"\x0eNewsCollectReq\x12'\n" +
	"\x04page\x18\x01 \x01(\v2\x13.common.PageRequestR\x04page\"k\n" +
	"\x12NewsCollectResData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.islamic.v1.ArticleInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\"\x8f\x01\n" +
	"\x0eNewsCollectRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x122\n" +
	"\x04data\x18\x04 \x01(\v2\x1e.islamic.v1.NewsCollectResDataR\x04data\":\n" +
	"\x19NewsCollectStatusCheckReq\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x05R\tarticleId\";\n" +
	"\x1aNewsCollectStatusCheckData\x12\x1d\n" +
	"\n" +
	"is_collect\x18\x01 \x01(\x05R\tisCollect\"\xa2\x01\n" +
	"\x19NewsCollectStatusCheckRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x12:\n" +
	"\x04data\x18\x04 \x01(\v2&.islamic.v1.NewsCollectStatusCheckDataR\x04data\"1\n" +
	"\x10NewsCollectOpReq\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x05R\tarticleId\"]\n" +
	"\x10NewsCollectOpRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"/\n" +
	"\x0eNewsShareOpReq\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x05R\tarticleId\"[\n" +
	"\x0eNewsShareOpRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\".\n" +
	"\rNewsViewOpReq\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\x05R\tarticleId\"Z\n" +
	"\rNewsViewOpRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\"\x12\n" +
	"\x10NewsTopicListReq\"\x99\x01\n" +
	"\tTopicInfo\x12\x19\n" +
	"\btopic_id\x18\x01 \x01(\rR\atopicId\x12\x1f\n" +
	"\vlanguage_id\x18\x02 \x01(\rR\n" +
	"languageId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1d\n" +
	"\n" +
	"short_name\x18\x04 \x01(\tR\tshortName\x12\x1d\n" +
	"\n" +
	"topic_imgs\x18\x05 \x01(\tR\ttopicImgs\"A\n" +
	"\x14NewsTopicListResData\x12)\n" +
	"\x04list\x18\x01 \x03(\v2\x15.islamic.v1.TopicInfoR\x04list\"\x93\x01\n" +
	"\x10NewsTopicListRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x124\n" +
	"\x04data\x18\x04 \x01(\v2 .islamic.v1.NewsTopicListResDataR\x04data\"Z\n" +
	"\x14NewsListByTopicIdReq\x12\x19\n" +
	"\btopic_id\x18\x01 \x01(\rR\atopicId\x12'\n" +
	"\x04page\x18\x02 \x01(\v2\x13.common.PageRequestR\x04page\"\xea\x02\n" +
	"\vArticleInfo\x12\x1d\n" +
	"\n" +
	"article_id\x18\x01 \x01(\rR\tarticleId\x12\x1f\n" +
	"\vlanguage_id\x18\x02 \x01(\rR\n" +
	"languageId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x18\n" +
	"\acontent\x18\x04 \x01(\tR\acontent\x12\x1f\n" +
	"\vcategory_id\x18\x05 \x01(\rR\n" +
	"categoryId\x12#\n" +
	"\rcategory_name\x18\x06 \x01(\tR\fcategoryName\x12\x1d\n" +
	"\n" +
	"cover_imgs\x18\a \x01(\tR\tcoverImgs\x12\x16\n" +
	"\x06author\x18\b \x01(\tR\x06author\x12!\n" +
	"\fpublish_time\x18\t \x01(\x03R\vpublishTime\x12\x1f\n" +
	"\vauthor_logo\x18\n" +
	" \x01(\tR\n" +
	"authorLogo\x12,\n" +
	"\x12author_auth_status\x18\v \x01(\rR\x10authorAuthStatus\"\xad\x01\n" +
	"\x18NewsListByTopicIdResData\x12+\n" +
	"\x04list\x18\x01 \x03(\v2\x17.islamic.v1.ArticleInfoR\x04list\x12(\n" +
	"\x04page\x18\x02 \x01(\v2\x14.common.PageResponseR\x04page\x12\x1d\n" +
	"\n" +
	"topic_name\x18\x03 \x01(\tR\ttopicName\x12\x1b\n" +
	"\ttopic_img\x18\x04 \x01(\tR\btopicImg\"\x9b\x01\n" +
	"\x14NewsListByTopicIdRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x128\n" +
	"\x04data\x18\x05 \x01(\v2$.islamic.v1.NewsListByTopicIdResDataR\x04data\"\x11\n" +
	"\x0fUserBookmarkReq\"\xa5\x01\n" +
	"\x13UserBookmarkResData\x12\x1f\n" +
	"\vayah_counts\x18\x01 \x01(\rR\n" +
	"ayahCounts\x12\x1d\n" +
	"\n" +
	"doa_counts\x18\x02 \x01(\rR\tdoaCounts\x12'\n" +
	"\x0factivity_counts\x18\x03 \x01(\rR\x0eactivityCounts\x12%\n" +
	"\x0earticle_counts\x18\x04 \x01(\rR\rarticleCounts\"\x91\x01\n" +
	"\x0fUserBookmarkRes\x12\x12\n" +
	"\x04code\x18\x01 \x01(\x05R\x04code\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12#\n" +
	"\x05error\x18\x03 \x01(\v2\r.common.ErrorR\x05error\x123\n" +
	"\x04data\x18\x05 \x01(\v2\x1f.islamic.v1.UserBookmarkResDataR\x04data2\xfd\b\n" +
	"\vNewsService\x12`\n" +
	"\x14UserNewsCategoryEdit\x12#.islamic.v1.UserNewsCategoryEditReq\x1a#.islamic.v1.UserNewsCategoryEditRes\x12`\n" +
	"\x14UserNewsCategoryList\x12#.islamic.v1.UserNewsCategoryListReq\x1a#.islamic.v1.UserNewsCategoryListRes\x12T\n" +
	"\x10NewsCategoryList\x12\x1f.islamic.v1.NewsCategoryListReq\x1a\x1f.islamic.v1.NewsCategoryListRes\x12T\n" +
	"\x10NewsListByCateId\x12\x1f.islamic.v1.NewsListByCateIdReq\x1a\x1f.islamic.v1.NewsListByCateIdRes\x12K\n" +
	"\rNewsTopicList\x12\x1c.islamic.v1.NewsTopicListReq\x1a\x1c.islamic.v1.NewsTopicListRes\x12W\n" +
	"\x11NewsListByTopicId\x12 .islamic.v1.NewsListByTopicIdReq\x1a .islamic.v1.NewsListByTopicIdRes\x12<\n" +
	"\bNewsInfo\x12\x17.islamic.v1.NewsInfoReq\x1a\x17.islamic.v1.NewsInfoRes\x12E\n" +
	"\vNewsHotList\x12\x1a.islamic.v1.NewsHotListReq\x1a\x1a.islamic.v1.NewsHotListRes\x12I\n" +
	"\x0fNewsCollectList\x12\x1a.islamic.v1.NewsCollectReq\x1a\x1a.islamic.v1.NewsCollectRes\x12f\n" +
	"\x16NewsCollectStatusCheck\x12%.islamic.v1.NewsCollectStatusCheckReq\x1a%.islamic.v1.NewsCollectStatusCheckRes\x12K\n" +
	"\rNewsCollectOp\x12\x1c.islamic.v1.NewsCollectOpReq\x1a\x1c.islamic.v1.NewsCollectOpRes\x12E\n" +
	"\vNewsShareOp\x12\x1a.islamic.v1.NewsShareOpReq\x1a\x1a.islamic.v1.NewsShareOpRes\x12B\n" +
	"\n" +
	"NewsViewOp\x12\x19.islamic.v1.NewsViewOpReq\x1a\x19.islamic.v1.NewsViewOpRes\x12H\n" +
	"\fUserBookmark\x12\x1b.islamic.v1.UserBookmarkReq\x1a\x1b.islamic.v1.UserBookmarkResB<Z:halalplus/app/islamic-content-svc/api/islamic/v1;islamicv1b\x06proto3"

var (
	file_islamic_v1_news_proto_rawDescOnce sync.Once
	file_islamic_v1_news_proto_rawDescData []byte
)

func file_islamic_v1_news_proto_rawDescGZIP() []byte {
	file_islamic_v1_news_proto_rawDescOnce.Do(func() {
		file_islamic_v1_news_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_islamic_v1_news_proto_rawDesc), len(file_islamic_v1_news_proto_rawDesc)))
	})
	return file_islamic_v1_news_proto_rawDescData
}

var file_islamic_v1_news_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_islamic_v1_news_proto_goTypes = []any{
	(*UserNewsCategoryEditReq)(nil),     // 0: islamic.v1.UserNewsCategoryEditReq
	(*UserNewsCategoryEditRes)(nil),     // 1: islamic.v1.UserNewsCategoryEditRes
	(*UserNewsCategoryListReq)(nil),     // 2: islamic.v1.UserNewsCategoryListReq
	(*UserNewsCategoryListResData)(nil), // 3: islamic.v1.UserNewsCategoryListResData
	(*UserNewsCategoryListRes)(nil),     // 4: islamic.v1.UserNewsCategoryListRes
	(*NewsCategoryListReq)(nil),         // 5: islamic.v1.NewsCategoryListReq
	(*CategoryInfo)(nil),                // 6: islamic.v1.CategoryInfo
	(*NewsCategoryListResData)(nil),     // 7: islamic.v1.NewsCategoryListResData
	(*NewsCategoryListRes)(nil),         // 8: islamic.v1.NewsCategoryListRes
	(*NewsListByCateIdReq)(nil),         // 9: islamic.v1.NewsListByCateIdReq
	(*NewsListByCateIdResData)(nil),     // 10: islamic.v1.NewsListByCateIdResData
	(*NewsListByCateIdRes)(nil),         // 11: islamic.v1.NewsListByCateIdRes
	(*NewsHotListReq)(nil),              // 12: islamic.v1.NewsHotListReq
	(*NewsHotListResData)(nil),          // 13: islamic.v1.NewsHotListResData
	(*NewsHotListRes)(nil),              // 14: islamic.v1.NewsHotListRes
	(*NewsInfoReq)(nil),                 // 15: islamic.v1.NewsInfoReq
	(*NewsInfoRes)(nil),                 // 16: islamic.v1.NewsInfoRes
	(*NewsCollectReq)(nil),              // 17: islamic.v1.NewsCollectReq
	(*NewsCollectResData)(nil),          // 18: islamic.v1.NewsCollectResData
	(*NewsCollectRes)(nil),              // 19: islamic.v1.NewsCollectRes
	(*NewsCollectStatusCheckReq)(nil),   // 20: islamic.v1.NewsCollectStatusCheckReq
	(*NewsCollectStatusCheckData)(nil),  // 21: islamic.v1.NewsCollectStatusCheckData
	(*NewsCollectStatusCheckRes)(nil),   // 22: islamic.v1.NewsCollectStatusCheckRes
	(*NewsCollectOpReq)(nil),            // 23: islamic.v1.NewsCollectOpReq
	(*NewsCollectOpRes)(nil),            // 24: islamic.v1.NewsCollectOpRes
	(*NewsShareOpReq)(nil),              // 25: islamic.v1.NewsShareOpReq
	(*NewsShareOpRes)(nil),              // 26: islamic.v1.NewsShareOpRes
	(*NewsViewOpReq)(nil),               // 27: islamic.v1.NewsViewOpReq
	(*NewsViewOpRes)(nil),               // 28: islamic.v1.NewsViewOpRes
	(*NewsTopicListReq)(nil),            // 29: islamic.v1.NewsTopicListReq
	(*TopicInfo)(nil),                   // 30: islamic.v1.TopicInfo
	(*NewsTopicListResData)(nil),        // 31: islamic.v1.NewsTopicListResData
	(*NewsTopicListRes)(nil),            // 32: islamic.v1.NewsTopicListRes
	(*NewsListByTopicIdReq)(nil),        // 33: islamic.v1.NewsListByTopicIdReq
	(*ArticleInfo)(nil),                 // 34: islamic.v1.ArticleInfo
	(*NewsListByTopicIdResData)(nil),    // 35: islamic.v1.NewsListByTopicIdResData
	(*NewsListByTopicIdRes)(nil),        // 36: islamic.v1.NewsListByTopicIdRes
	(*UserBookmarkReq)(nil),             // 37: islamic.v1.UserBookmarkReq
	(*UserBookmarkResData)(nil),         // 38: islamic.v1.UserBookmarkResData
	(*UserBookmarkRes)(nil),             // 39: islamic.v1.UserBookmarkRes
	(*common.Error)(nil),                // 40: common.Error
	(*common.PageRequest)(nil),          // 41: common.PageRequest
	(*common.PageResponse)(nil),         // 42: common.PageResponse
}
var file_islamic_v1_news_proto_depIdxs = []int32{
	40, // 0: islamic.v1.UserNewsCategoryEditRes.error:type_name -> common.Error
	6,  // 1: islamic.v1.UserNewsCategoryListResData.unSelectList:type_name -> islamic.v1.CategoryInfo
	6,  // 2: islamic.v1.UserNewsCategoryListResData.selectList:type_name -> islamic.v1.CategoryInfo
	40, // 3: islamic.v1.UserNewsCategoryListRes.error:type_name -> common.Error
	3,  // 4: islamic.v1.UserNewsCategoryListRes.data:type_name -> islamic.v1.UserNewsCategoryListResData
	6,  // 5: islamic.v1.NewsCategoryListResData.list:type_name -> islamic.v1.CategoryInfo
	40, // 6: islamic.v1.NewsCategoryListRes.error:type_name -> common.Error
	7,  // 7: islamic.v1.NewsCategoryListRes.data:type_name -> islamic.v1.NewsCategoryListResData
	41, // 8: islamic.v1.NewsListByCateIdReq.page:type_name -> common.PageRequest
	34, // 9: islamic.v1.NewsListByCateIdResData.list:type_name -> islamic.v1.ArticleInfo
	42, // 10: islamic.v1.NewsListByCateIdResData.page:type_name -> common.PageResponse
	40, // 11: islamic.v1.NewsListByCateIdRes.error:type_name -> common.Error
	10, // 12: islamic.v1.NewsListByCateIdRes.data:type_name -> islamic.v1.NewsListByCateIdResData
	41, // 13: islamic.v1.NewsHotListReq.page:type_name -> common.PageRequest
	34, // 14: islamic.v1.NewsHotListResData.list:type_name -> islamic.v1.ArticleInfo
	42, // 15: islamic.v1.NewsHotListResData.page:type_name -> common.PageResponse
	40, // 16: islamic.v1.NewsHotListRes.error:type_name -> common.Error
	13, // 17: islamic.v1.NewsHotListRes.data:type_name -> islamic.v1.NewsHotListResData
	40, // 18: islamic.v1.NewsInfoRes.error:type_name -> common.Error
	34, // 19: islamic.v1.NewsInfoRes.data:type_name -> islamic.v1.ArticleInfo
	41, // 20: islamic.v1.NewsCollectReq.page:type_name -> common.PageRequest
	34, // 21: islamic.v1.NewsCollectResData.list:type_name -> islamic.v1.ArticleInfo
	42, // 22: islamic.v1.NewsCollectResData.page:type_name -> common.PageResponse
	40, // 23: islamic.v1.NewsCollectRes.error:type_name -> common.Error
	18, // 24: islamic.v1.NewsCollectRes.data:type_name -> islamic.v1.NewsCollectResData
	40, // 25: islamic.v1.NewsCollectStatusCheckRes.error:type_name -> common.Error
	21, // 26: islamic.v1.NewsCollectStatusCheckRes.data:type_name -> islamic.v1.NewsCollectStatusCheckData
	40, // 27: islamic.v1.NewsCollectOpRes.error:type_name -> common.Error
	40, // 28: islamic.v1.NewsShareOpRes.error:type_name -> common.Error
	40, // 29: islamic.v1.NewsViewOpRes.error:type_name -> common.Error
	30, // 30: islamic.v1.NewsTopicListResData.list:type_name -> islamic.v1.TopicInfo
	40, // 31: islamic.v1.NewsTopicListRes.error:type_name -> common.Error
	31, // 32: islamic.v1.NewsTopicListRes.data:type_name -> islamic.v1.NewsTopicListResData
	41, // 33: islamic.v1.NewsListByTopicIdReq.page:type_name -> common.PageRequest
	34, // 34: islamic.v1.NewsListByTopicIdResData.list:type_name -> islamic.v1.ArticleInfo
	42, // 35: islamic.v1.NewsListByTopicIdResData.page:type_name -> common.PageResponse
	40, // 36: islamic.v1.NewsListByTopicIdRes.error:type_name -> common.Error
	35, // 37: islamic.v1.NewsListByTopicIdRes.data:type_name -> islamic.v1.NewsListByTopicIdResData
	40, // 38: islamic.v1.UserBookmarkRes.error:type_name -> common.Error
	38, // 39: islamic.v1.UserBookmarkRes.data:type_name -> islamic.v1.UserBookmarkResData
	0,  // 40: islamic.v1.NewsService.UserNewsCategoryEdit:input_type -> islamic.v1.UserNewsCategoryEditReq
	2,  // 41: islamic.v1.NewsService.UserNewsCategoryList:input_type -> islamic.v1.UserNewsCategoryListReq
	5,  // 42: islamic.v1.NewsService.NewsCategoryList:input_type -> islamic.v1.NewsCategoryListReq
	9,  // 43: islamic.v1.NewsService.NewsListByCateId:input_type -> islamic.v1.NewsListByCateIdReq
	29, // 44: islamic.v1.NewsService.NewsTopicList:input_type -> islamic.v1.NewsTopicListReq
	33, // 45: islamic.v1.NewsService.NewsListByTopicId:input_type -> islamic.v1.NewsListByTopicIdReq
	15, // 46: islamic.v1.NewsService.NewsInfo:input_type -> islamic.v1.NewsInfoReq
	12, // 47: islamic.v1.NewsService.NewsHotList:input_type -> islamic.v1.NewsHotListReq
	17, // 48: islamic.v1.NewsService.NewsCollectList:input_type -> islamic.v1.NewsCollectReq
	20, // 49: islamic.v1.NewsService.NewsCollectStatusCheck:input_type -> islamic.v1.NewsCollectStatusCheckReq
	23, // 50: islamic.v1.NewsService.NewsCollectOp:input_type -> islamic.v1.NewsCollectOpReq
	25, // 51: islamic.v1.NewsService.NewsShareOp:input_type -> islamic.v1.NewsShareOpReq
	27, // 52: islamic.v1.NewsService.NewsViewOp:input_type -> islamic.v1.NewsViewOpReq
	37, // 53: islamic.v1.NewsService.UserBookmark:input_type -> islamic.v1.UserBookmarkReq
	1,  // 54: islamic.v1.NewsService.UserNewsCategoryEdit:output_type -> islamic.v1.UserNewsCategoryEditRes
	4,  // 55: islamic.v1.NewsService.UserNewsCategoryList:output_type -> islamic.v1.UserNewsCategoryListRes
	8,  // 56: islamic.v1.NewsService.NewsCategoryList:output_type -> islamic.v1.NewsCategoryListRes
	11, // 57: islamic.v1.NewsService.NewsListByCateId:output_type -> islamic.v1.NewsListByCateIdRes
	32, // 58: islamic.v1.NewsService.NewsTopicList:output_type -> islamic.v1.NewsTopicListRes
	36, // 59: islamic.v1.NewsService.NewsListByTopicId:output_type -> islamic.v1.NewsListByTopicIdRes
	16, // 60: islamic.v1.NewsService.NewsInfo:output_type -> islamic.v1.NewsInfoRes
	14, // 61: islamic.v1.NewsService.NewsHotList:output_type -> islamic.v1.NewsHotListRes
	19, // 62: islamic.v1.NewsService.NewsCollectList:output_type -> islamic.v1.NewsCollectRes
	22, // 63: islamic.v1.NewsService.NewsCollectStatusCheck:output_type -> islamic.v1.NewsCollectStatusCheckRes
	24, // 64: islamic.v1.NewsService.NewsCollectOp:output_type -> islamic.v1.NewsCollectOpRes
	26, // 65: islamic.v1.NewsService.NewsShareOp:output_type -> islamic.v1.NewsShareOpRes
	28, // 66: islamic.v1.NewsService.NewsViewOp:output_type -> islamic.v1.NewsViewOpRes
	39, // 67: islamic.v1.NewsService.UserBookmark:output_type -> islamic.v1.UserBookmarkRes
	54, // [54:68] is the sub-list for method output_type
	40, // [40:54] is the sub-list for method input_type
	40, // [40:40] is the sub-list for extension type_name
	40, // [40:40] is the sub-list for extension extendee
	0,  // [0:40] is the sub-list for field type_name
}

func init() { file_islamic_v1_news_proto_init() }
func file_islamic_v1_news_proto_init() {
	if File_islamic_v1_news_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_islamic_v1_news_proto_rawDesc), len(file_islamic_v1_news_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_islamic_v1_news_proto_goTypes,
		DependencyIndexes: file_islamic_v1_news_proto_depIdxs,
		MessageInfos:      file_islamic_v1_news_proto_msgTypes,
	}.Build()
	File_islamic_v1_news_proto = out.File
	file_islamic_v1_news_proto_goTypes = nil
	file_islamic_v1_news_proto_depIdxs = nil
}
