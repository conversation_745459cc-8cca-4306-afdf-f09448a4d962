// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/faq_cate.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type FaqCate struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                                            //
	IsZh          uint32                 `protobuf:"varint,2,opt,name=IsZh,proto3" json:"IsZh,omitempty" dc:"是否中文，0-否，1-是"`                      // 是否中文，0-否，1-是
	IsEn          uint32                 `protobuf:"varint,3,opt,name=IsEn,proto3" json:"IsEn,omitempty" dc:"是否英文，0-否，1-是"`                      // 是否英文，0-否，1-是
	IsId          uint32                 `protobuf:"varint,4,opt,name=IsId,proto3" json:"IsId,omitempty" dc:"是否印尼文，0-否，1-是"`                     // 是否印尼文，0-否，1-是
	IsPublish     int32                  `protobuf:"varint,5,opt,name=IsPublish,proto3" json:"IsPublish,omitempty" dc:"状态 [ 0 待发布 1 已发布 2 已下线]"` // 状态 [ 0 待发布 1 已发布 2 已下线]
	Sort          int32                  `protobuf:"varint,6,opt,name=Sort,proto3" json:"Sort,omitempty" dc:"排序"`                                // 排序
	CateCount     int32                  `protobuf:"varint,7,opt,name=CateCount,proto3" json:"CateCount,omitempty" dc:"分类下的文章总数"`                // 分类下的文章总数
	CreateAccount string                 `protobuf:"bytes,8,opt,name=CreateAccount,proto3" json:"CreateAccount,omitempty" dc:"创建者"`              // 创建者
	UpdateAccount string                 `protobuf:"bytes,9,opt,name=UpdateAccount,proto3" json:"UpdateAccount,omitempty" dc:"更新者"`              // 更新者
	CreateTime    int64                  `protobuf:"varint,10,opt,name=CreateTime,proto3" json:"CreateTime,omitempty" dc:"创建时间(毫秒时间戳)"`          // 创建时间(毫秒时间戳)
	UpdateTime    int64                  `protobuf:"varint,11,opt,name=UpdateTime,proto3" json:"UpdateTime,omitempty" dc:"更新时间(毫秒时间戳)"`          // 更新时间(毫秒时间戳)
	DeleteTime    int64                  `protobuf:"varint,12,opt,name=DeleteTime,proto3" json:"DeleteTime,omitempty"`                           //
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FaqCate) Reset() {
	*x = FaqCate{}
	mi := &file_pbentity_faq_cate_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FaqCate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FaqCate) ProtoMessage() {}

func (x *FaqCate) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_faq_cate_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FaqCate.ProtoReflect.Descriptor instead.
func (*FaqCate) Descriptor() ([]byte, []int) {
	return file_pbentity_faq_cate_proto_rawDescGZIP(), []int{0}
}

func (x *FaqCate) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *FaqCate) GetIsZh() uint32 {
	if x != nil {
		return x.IsZh
	}
	return 0
}

func (x *FaqCate) GetIsEn() uint32 {
	if x != nil {
		return x.IsEn
	}
	return 0
}

func (x *FaqCate) GetIsId() uint32 {
	if x != nil {
		return x.IsId
	}
	return 0
}

func (x *FaqCate) GetIsPublish() int32 {
	if x != nil {
		return x.IsPublish
	}
	return 0
}

func (x *FaqCate) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *FaqCate) GetCateCount() int32 {
	if x != nil {
		return x.CateCount
	}
	return 0
}

func (x *FaqCate) GetCreateAccount() string {
	if x != nil {
		return x.CreateAccount
	}
	return ""
}

func (x *FaqCate) GetUpdateAccount() string {
	if x != nil {
		return x.UpdateAccount
	}
	return ""
}

func (x *FaqCate) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *FaqCate) GetUpdateTime() int64 {
	if x != nil {
		return x.UpdateTime
	}
	return 0
}

func (x *FaqCate) GetDeleteTime() int64 {
	if x != nil {
		return x.DeleteTime
	}
	return 0
}

var File_pbentity_faq_cate_proto protoreflect.FileDescriptor

const file_pbentity_faq_cate_proto_rawDesc = "" +
	"\n" +
	"\x17pbentity/faq_cate.proto\x12\bpbentity\"\xd1\x02\n" +
	"\aFaqCate\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x12\n" +
	"\x04IsZh\x18\x02 \x01(\rR\x04IsZh\x12\x12\n" +
	"\x04IsEn\x18\x03 \x01(\rR\x04IsEn\x12\x12\n" +
	"\x04IsId\x18\x04 \x01(\rR\x04IsId\x12\x1c\n" +
	"\tIsPublish\x18\x05 \x01(\x05R\tIsPublish\x12\x12\n" +
	"\x04Sort\x18\x06 \x01(\x05R\x04Sort\x12\x1c\n" +
	"\tCateCount\x18\a \x01(\x05R\tCateCount\x12$\n" +
	"\rCreateAccount\x18\b \x01(\tR\rCreateAccount\x12$\n" +
	"\rUpdateAccount\x18\t \x01(\tR\rUpdateAccount\x12\x1e\n" +
	"\n" +
	"CreateTime\x18\n" +
	" \x01(\x03R\n" +
	"CreateTime\x12\x1e\n" +
	"\n" +
	"UpdateTime\x18\v \x01(\x03R\n" +
	"UpdateTime\x12\x1e\n" +
	"\n" +
	"DeleteTime\x18\f \x01(\x03R\n" +
	"DeleteTimeB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_faq_cate_proto_rawDescOnce sync.Once
	file_pbentity_faq_cate_proto_rawDescData []byte
)

func file_pbentity_faq_cate_proto_rawDescGZIP() []byte {
	file_pbentity_faq_cate_proto_rawDescOnce.Do(func() {
		file_pbentity_faq_cate_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_faq_cate_proto_rawDesc), len(file_pbentity_faq_cate_proto_rawDesc)))
	})
	return file_pbentity_faq_cate_proto_rawDescData
}

var file_pbentity_faq_cate_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_faq_cate_proto_goTypes = []any{
	(*FaqCate)(nil), // 0: pbentity.FaqCate
}
var file_pbentity_faq_cate_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_faq_cate_proto_init() }
func file_pbentity_faq_cate_proto_init() {
	if File_pbentity_faq_cate_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_faq_cate_proto_rawDesc), len(file_pbentity_faq_cate_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_faq_cate_proto_goTypes,
		DependencyIndexes: file_pbentity_faq_cate_proto_depIdxs,
		MessageInfos:      file_pbentity_faq_cate_proto_msgTypes,
	}.Build()
	File_pbentity_faq_cate_proto = out.File
	file_pbentity_faq_cate_proto_goTypes = nil
	file_pbentity_faq_cate_proto_depIdxs = nil
}
