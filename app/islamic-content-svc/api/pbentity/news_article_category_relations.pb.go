// ==========================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// ==========================================================================

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v5.27.2
// source: pbentity/news_article_category_relations.proto

package pbentity

import (
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type NewsArticleCategoryRelations struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint32                 `protobuf:"varint,1,opt,name=Id,proto3" json:"Id,omitempty"`                           //
	ArticleId     uint32                 `protobuf:"varint,2,opt,name=ArticleId,proto3" json:"ArticleId,omitempty" dc:"文章id"`   // 文章id
	CategoryId    uint32                 `protobuf:"varint,3,opt,name=CategoryId,proto3" json:"CategoryId,omitempty" dc:"分类id"` // 分类id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NewsArticleCategoryRelations) Reset() {
	*x = NewsArticleCategoryRelations{}
	mi := &file_pbentity_news_article_category_relations_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NewsArticleCategoryRelations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NewsArticleCategoryRelations) ProtoMessage() {}

func (x *NewsArticleCategoryRelations) ProtoReflect() protoreflect.Message {
	mi := &file_pbentity_news_article_category_relations_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NewsArticleCategoryRelations.ProtoReflect.Descriptor instead.
func (*NewsArticleCategoryRelations) Descriptor() ([]byte, []int) {
	return file_pbentity_news_article_category_relations_proto_rawDescGZIP(), []int{0}
}

func (x *NewsArticleCategoryRelations) GetId() uint32 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *NewsArticleCategoryRelations) GetArticleId() uint32 {
	if x != nil {
		return x.ArticleId
	}
	return 0
}

func (x *NewsArticleCategoryRelations) GetCategoryId() uint32 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

var File_pbentity_news_article_category_relations_proto protoreflect.FileDescriptor

const file_pbentity_news_article_category_relations_proto_rawDesc = "" +
	"\n" +
	".pbentity/news_article_category_relations.proto\x12\bpbentity\"l\n" +
	"\x1cNewsArticleCategoryRelations\x12\x0e\n" +
	"\x02Id\x18\x01 \x01(\rR\x02Id\x12\x1c\n" +
	"\tArticleId\x18\x02 \x01(\rR\tArticleId\x12\x1e\n" +
	"\n" +
	"CategoryId\x18\x03 \x01(\rR\n" +
	"CategoryIdB0Z.halalplus/app/islamic-content-svc/api/pbentityb\x06proto3"

var (
	file_pbentity_news_article_category_relations_proto_rawDescOnce sync.Once
	file_pbentity_news_article_category_relations_proto_rawDescData []byte
)

func file_pbentity_news_article_category_relations_proto_rawDescGZIP() []byte {
	file_pbentity_news_article_category_relations_proto_rawDescOnce.Do(func() {
		file_pbentity_news_article_category_relations_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_pbentity_news_article_category_relations_proto_rawDesc), len(file_pbentity_news_article_category_relations_proto_rawDesc)))
	})
	return file_pbentity_news_article_category_relations_proto_rawDescData
}

var file_pbentity_news_article_category_relations_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_pbentity_news_article_category_relations_proto_goTypes = []any{
	(*NewsArticleCategoryRelations)(nil), // 0: pbentity.NewsArticleCategoryRelations
}
var file_pbentity_news_article_category_relations_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_pbentity_news_article_category_relations_proto_init() }
func file_pbentity_news_article_category_relations_proto_init() {
	if File_pbentity_news_article_category_relations_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_pbentity_news_article_category_relations_proto_rawDesc), len(file_pbentity_news_article_category_relations_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_pbentity_news_article_category_relations_proto_goTypes,
		DependencyIndexes: file_pbentity_news_article_category_relations_proto_depIdxs,
		MessageInfos:      file_pbentity_news_article_category_relations_proto_msgTypes,
	}.Build()
	File_pbentity_news_article_category_relations_proto = out.File
	file_pbentity_news_article_category_relations_proto_goTypes = nil
	file_pbentity_news_article_category_relations_proto_depIdxs = nil
}
