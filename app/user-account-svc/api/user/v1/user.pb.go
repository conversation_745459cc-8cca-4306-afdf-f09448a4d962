// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.32.0
// 	protoc        v3.21.12
// source: user/v1/user.proto

package v1

import (
	common "halalplus/api/common"
	reflect "reflect"
	sync "sync"

	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Gender int32

const (
	Gender_UNKNOWN Gender = 0
	Gender_MALE    Gender = 1
	Gender_FEMALE  Gender = 2
)

// Enum value maps for Gender.
var (
	Gender_name = map[int32]string{
		0: "UNKNOWN",
		1: "MALE",
		2: "FEMALE",
	}
	Gender_value = map[string]int32{
		"UNKNOWN": 0,
		"MALE":    1,
		"FEMALE":  2,
	}
)

func (x Gender) Enum() *Gender {
	p := new(Gender)
	*p = x
	return p
}

func (x Gender) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Gender) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[0].Descriptor()
}

func (Gender) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[0]
}

func (x Gender) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Gender.Descriptor instead.
func (Gender) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

// 密码登录的账号类型
type SignInType int32

const (
	SignInType_SIGN_IN_TYPE_UNKNOWN SignInType = 0
	SignInType_SIGN_IN_TYPE_ACCOUNT SignInType = 1
	SignInType_SIGN_IN_TYPE_EMAIL   SignInType = 2
)

// Enum value maps for SignInType.
var (
	SignInType_name = map[int32]string{
		0: "SIGN_IN_TYPE_UNKNOWN",
		1: "SIGN_IN_TYPE_ACCOUNT",
		2: "SIGN_IN_TYPE_EMAIL",
	}
	SignInType_value = map[string]int32{
		"SIGN_IN_TYPE_UNKNOWN": 0,
		"SIGN_IN_TYPE_ACCOUNT": 1,
		"SIGN_IN_TYPE_EMAIL":   2,
	}
)

func (x SignInType) Enum() *SignInType {
	p := new(SignInType)
	*p = x
	return p
}

func (x SignInType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SignInType) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[1].Descriptor()
}

func (SignInType) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[1]
}

func (x SignInType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SignInType.Descriptor instead.
func (SignInType) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

// 验证码用途场景定义
type VerifyCodeScene int32

const (
	// 未指定场景，后端应拒绝处理该请求
	VerifyCodeScene_VERIFY_CODE_SCENE_UNSPECIFIED VerifyCodeScene = 0
	// 用户登录时使用的验证码（手机号登录、验证码登录）
	VerifyCodeScene_LOGIN VerifyCodeScene = 1
	// 用户注册时使用的验证码（注册新账号）
	VerifyCodeScene_SIGN_UP VerifyCodeScene = 2
	// 重置密码时使用的验证码（找回/忘记密码）
	VerifyCodeScene_RESET_PASSWORD VerifyCodeScene = 3
	// 绑定手机号时使用的验证码（用于增强账户安全或添加手机号）
	VerifyCodeScene_BIND_PHONE VerifyCodeScene = 4
	// 验证我的手机号, 用于更换手机号的前提条件
	VerifyCodeScene_MY_PHONE VerifyCodeScene = 5
	// 验证我的邮箱, 用于更换邮箱的前提条件
	VerifyCodeScene_MY_EMAIL VerifyCodeScene = 6
)

// Enum value maps for VerifyCodeScene.
var (
	VerifyCodeScene_name = map[int32]string{
		0: "VERIFY_CODE_SCENE_UNSPECIFIED",
		1: "LOGIN",
		2: "SIGN_UP",
		3: "RESET_PASSWORD",
		4: "BIND_PHONE",
		5: "MY_PHONE",
		6: "MY_EMAIL",
	}
	VerifyCodeScene_value = map[string]int32{
		"VERIFY_CODE_SCENE_UNSPECIFIED": 0,
		"LOGIN":                         1,
		"SIGN_UP":                       2,
		"RESET_PASSWORD":                3,
		"BIND_PHONE":                    4,
		"MY_PHONE":                      5,
		"MY_EMAIL":                      6,
	}
)

func (x VerifyCodeScene) Enum() *VerifyCodeScene {
	p := new(VerifyCodeScene)
	*p = x
	return p
}

func (x VerifyCodeScene) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyCodeScene) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[2].Descriptor()
}

func (VerifyCodeScene) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[2]
}

func (x VerifyCodeScene) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyCodeScene.Descriptor instead.
func (VerifyCodeScene) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

// 验证码发送渠道
type VerifyCodeChannel int32

const (
	// 未指定发送渠道，表示非法请求，后端应返回错误
	VerifyCodeChannel_UNSPECIFIED VerifyCodeChannel = 0
	// 通过短信（SMS）发送验证码。适用于大多数用户，尤其是国内用户。
	VerifyCodeChannel_SMS VerifyCodeChannel = 1
	// 通过 WhatsApp 发送验证码。适用于国际用户或不支持短信的区域。
	VerifyCodeChannel_WHATSAPP VerifyCodeChannel = 2
	// 通过邮箱发送验证码。适用于邮箱注册/找回密码等场景。
	VerifyCodeChannel_EMAIL VerifyCodeChannel = 3
	// 通过语音电话播报验证码。适用于用户无法接收短信时的兜底方式。
	VerifyCodeChannel_VOICE VerifyCodeChannel = 4
)

// Enum value maps for VerifyCodeChannel.
var (
	VerifyCodeChannel_name = map[int32]string{
		0: "UNSPECIFIED",
		1: "SMS",
		2: "WHATSAPP",
		3: "EMAIL",
		4: "VOICE",
	}
	VerifyCodeChannel_value = map[string]int32{
		"UNSPECIFIED": 0,
		"SMS":         1,
		"WHATSAPP":    2,
		"EMAIL":       3,
		"VOICE":       4,
	}
)

func (x VerifyCodeChannel) Enum() *VerifyCodeChannel {
	p := new(VerifyCodeChannel)
	*p = x
	return p
}

func (x VerifyCodeChannel) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VerifyCodeChannel) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[3].Descriptor()
}

func (VerifyCodeChannel) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[3]
}

func (x VerifyCodeChannel) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VerifyCodeChannel.Descriptor instead.
func (VerifyCodeChannel) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

// UserInfo 用户信息结构
type UserInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id           uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" dc:"用户id"`                                                   // 用户id
	Account      string `protobuf:"bytes,2,opt,name=account,proto3" json:"account,omitempty" dc:"账号"`                                            // 账号
	CreateTime   int64  `protobuf:"varint,3,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty" dc:"注册时间"`                 // 注册时间
	Nickname     string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty" dc:"昵称"`                                          // 昵称
	Gender       Gender `protobuf:"varint,5,opt,name=gender,proto3,enum=user.v1.Gender" json:"gender,omitempty" dc:"性别：0未知 1男 2女"`               // 性别：0未知 1男 2女
	Avatar       string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty" dc:"头像url"`                                           // 头像url
	AreaCode     string `protobuf:"bytes,7,opt,name=area_code,json=areaCode,proto3" json:"area_code,omitempty" dc:"国家码（如 '62'）"`                 // 国家码（如 "62"）
	PhoneNum     string `protobuf:"bytes,8,opt,name=phone_num,json=phoneNum,proto3" json:"phone_num,omitempty" dc:"展示用户当前手机号码（中间部分脱敏，仅保留首尾各两位）"` // 展示用户当前手机号码（中间部分脱敏，仅保留首尾各两位）
	BindEmail    bool   `protobuf:"varint,9,opt,name=bind_email,json=bindEmail,proto3" json:"bind_email,omitempty" dc:"是否绑定email（0/1）"`          // 是否绑定email（0/1）
	BindPhone    bool   `protobuf:"varint,10,opt,name=bind_phone,json=bindPhone,proto3" json:"bind_phone,omitempty" dc:"是否绑定手机（0/1）"`            // 是否绑定手机（0/1）
	BindRealName bool   `protobuf:"varint,11,opt,name=bind_real_name,json=bindRealName,proto3" json:"bind_real_name,omitempty" dc:"是否实名认证（0/1）"` // 是否实名认证（0/1）
	FirstName    string `protobuf:"bytes,12,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName   string `protobuf:"bytes,13,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName     string `protobuf:"bytes,14,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Email        string `protobuf:"bytes,15,opt,name=email,proto3" json:"email,omitempty" dc:"用户邮箱"`                      // 用户邮箱
	IsGuest      bool   `protobuf:"varint,16,opt,name=is_guest,json=isGuest,proto3" json:"is_guest,omitempty" dc:"是否是游客"` // 是否是游客
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *UserInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *UserInfo) GetCreateTime() int64 {
	if x != nil {
		return x.CreateTime
	}
	return 0
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *UserInfo) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *UserInfo) GetAreaCode() string {
	if x != nil {
		return x.AreaCode
	}
	return ""
}

func (x *UserInfo) GetPhoneNum() string {
	if x != nil {
		return x.PhoneNum
	}
	return ""
}

func (x *UserInfo) GetBindEmail() bool {
	if x != nil {
		return x.BindEmail
	}
	return false
}

func (x *UserInfo) GetBindPhone() bool {
	if x != nil {
		return x.BindPhone
	}
	return false
}

func (x *UserInfo) GetBindRealName() bool {
	if x != nil {
		return x.BindRealName
	}
	return false
}

func (x *UserInfo) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UserInfo) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UserInfo) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetIsGuest() bool {
	if x != nil {
		return x.IsGuest
	}
	return false
}

// 注册请求
type SignUpReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account  string `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"`     // 用户名/手机号/邮箱
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码（加密/明文视业务）"` // 密码（加密/明文视业务）
}

func (x *SignUpReq) Reset() {
	*x = SignUpReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignUpReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpReq) ProtoMessage() {}

func (x *SignUpReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpReq.ProtoReflect.Descriptor instead.
func (*SignUpReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *SignUpReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignUpReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

// 注册响应
type SignUpRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SignUpRes) Reset() {
	*x = SignUpRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignUpRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignUpRes) ProtoMessage() {}

func (x *SignUpRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignUpRes.ProtoReflect.Descriptor instead.
func (*SignUpRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *SignUpRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignUpRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignUpRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 游客登录请求
type GuestSignInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *GuestSignInReq) Reset() {
	*x = GuestSignInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuestSignInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuestSignInReq) ProtoMessage() {}

func (x *GuestSignInReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuestSignInReq.ProtoReflect.Descriptor instead.
func (*GuestSignInReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *GuestSignInReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

// 登录响应
type GuestSignInRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *UserSignInResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *GuestSignInRes) Reset() {
	*x = GuestSignInRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GuestSignInRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GuestSignInRes) ProtoMessage() {}

func (x *GuestSignInRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GuestSignInRes.ProtoReflect.Descriptor instead.
func (*GuestSignInRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *GuestSignInRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *GuestSignInRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *GuestSignInRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *GuestSignInRes) GetData() *UserSignInResData {
	if x != nil {
		return x.Data
	}
	return nil
}

// 账号密码登录请求
type SignInReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account    string            `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"` // 用户名/手机号/邮箱
	Password   string            `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码"`       // 密码
	FrontInfo  *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	SignInType SignInType        `protobuf:"varint,4,opt,name=sign_in_type,json=signInType,proto3,enum=user.v1.SignInType" json:"sign_in_type,omitempty"`
}

func (x *SignInReq) Reset() {
	*x = SignInReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInReq) ProtoMessage() {}

func (x *SignInReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInReq.ProtoReflect.Descriptor instead.
func (*SignInReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *SignInReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignInReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SignInReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

func (x *SignInReq) GetSignInType() SignInType {
	if x != nil {
		return x.SignInType
	}
	return SignInType_SIGN_IN_TYPE_UNKNOWN
}

// 登录响应
type UserSignInRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32              `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string             `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *UserSignInResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UserSignInRes) Reset() {
	*x = UserSignInRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSignInRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignInRes) ProtoMessage() {}

func (x *UserSignInRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignInRes.ProtoReflect.Descriptor instead.
func (*UserSignInRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserSignInRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserSignInRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserSignInRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserSignInRes) GetData() *UserSignInResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserSignInResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token     string    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret    string    `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo  *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	SessionId string    `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty" dc:"客户端存储，用于api签名和更新token"` // 客户端存储，用于api签名和更新token
}

func (x *UserSignInResData) Reset() {
	*x = UserSignInResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserSignInResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserSignInResData) ProtoMessage() {}

func (x *UserSignInResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserSignInResData.ProtoReflect.Descriptor instead.
func (*UserSignInResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *UserSignInResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UserSignInResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *UserSignInResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *UserSignInResData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 登录（用户名）
type SignInByAccountReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Account   string            `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty" dc:"用户名/手机号/邮箱"` // 用户名/手机号/邮箱
	Password  string            `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty" dc:"密码"`       // 密码
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *SignInByAccountReq) Reset() {
	*x = SignInByAccountReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByAccountReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountReq) ProtoMessage() {}

func (x *SignInByAccountReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountReq.ProtoReflect.Descriptor instead.
func (*SignInByAccountReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{8}
}

func (x *SignInByAccountReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SignInByAccountReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SignInByAccountReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SignInByAccountRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                   `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                  `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error           `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SignInByAccountResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SignInByAccountRes) Reset() {
	*x = SignInByAccountRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByAccountRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountRes) ProtoMessage() {}

func (x *SignInByAccountRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountRes.ProtoReflect.Descriptor instead.
func (*SignInByAccountRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{9}
}

func (x *SignInByAccountRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignInByAccountRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignInByAccountRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SignInByAccountRes) GetData() *SignInByAccountResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignInByAccountResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token    string    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret   string    `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
}

func (x *SignInByAccountResData) Reset() {
	*x = SignInByAccountResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByAccountResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByAccountResData) ProtoMessage() {}

func (x *SignInByAccountResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByAccountResData.ProtoReflect.Descriptor instead.
func (*SignInByAccountResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{10}
}

func (x *SignInByAccountResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SignInByAccountResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *SignInByAccountResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

// 注册/登录方式：手机号短信验证（默认）
//
// 流程：
// 1. 用户输入手机号 (交互：纯数字键盘)
//   - 国际区号：+62（目前只开放+62）
//
// 2. 检查手机号格式
//   - 输入时实时校验：08 开头、长度 10～12 位
//
// 3. 判断是否为虚拟号/黑名单（通过服务端接口验证）
// 4. 请求发送验证码
//   - 冷却控制、防多次点击（60 秒倒计时）
//
// 5. 输入验证码
// 6. 验证成功 → 登录成功 / 注册新用户
type SignInByPhoneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneInfo *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	OptCode   string            `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *SignInByPhoneReq) Reset() {
	*x = SignInByPhoneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByPhoneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneReq) ProtoMessage() {}

func (x *SignInByPhoneReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneReq.ProtoReflect.Descriptor instead.
func (*SignInByPhoneReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{11}
}

func (x *SignInByPhoneReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SignInByPhoneReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *SignInByPhoneReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SignInByPhoneRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SignInByPhoneResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SignInByPhoneRes) Reset() {
	*x = SignInByPhoneRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByPhoneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneRes) ProtoMessage() {}

func (x *SignInByPhoneRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneRes.ProtoReflect.Descriptor instead.
func (*SignInByPhoneRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{12}
}

func (x *SignInByPhoneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignInByPhoneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignInByPhoneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SignInByPhoneRes) GetData() *SignInByPhoneResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignInByPhoneResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token     string    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret    string    `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo  *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	SessionId string    `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty" dc:"客户端存储，用于api签名和更新token"` // 客户端存储，用于api签名和更新token
}

func (x *SignInByPhoneResData) Reset() {
	*x = SignInByPhoneResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByPhoneResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByPhoneResData) ProtoMessage() {}

func (x *SignInByPhoneResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByPhoneResData.ProtoReflect.Descriptor instead.
func (*SignInByPhoneResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{13}
}

func (x *SignInByPhoneResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SignInByPhoneResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *SignInByPhoneResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *SignInByPhoneResData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

type SignInByEmailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email     string            `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	OptCode   string            `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *SignInByEmailReq) Reset() {
	*x = SignInByEmailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByEmailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByEmailReq) ProtoMessage() {}

func (x *SignInByEmailReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByEmailReq.ProtoReflect.Descriptor instead.
func (*SignInByEmailReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{14}
}

func (x *SignInByEmailReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *SignInByEmailReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *SignInByEmailReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SignInByEmailRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string                `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error         `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *SignInByEmailResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *SignInByEmailRes) Reset() {
	*x = SignInByEmailRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByEmailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByEmailRes) ProtoMessage() {}

func (x *SignInByEmailRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByEmailRes.ProtoReflect.Descriptor instead.
func (*SignInByEmailRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{15}
}

func (x *SignInByEmailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SignInByEmailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SignInByEmailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *SignInByEmailRes) GetData() *SignInByEmailResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type SignInByEmailResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token     string    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret    string    `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo  *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	SessionId string    `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty" dc:"客户端存储，用于api签名和更新token"` // 客户端存储，用于api签名和更新token
}

func (x *SignInByEmailResData) Reset() {
	*x = SignInByEmailResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SignInByEmailResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignInByEmailResData) ProtoMessage() {}

func (x *SignInByEmailResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignInByEmailResData.ProtoReflect.Descriptor instead.
func (*SignInByEmailResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{16}
}

func (x *SignInByEmailResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *SignInByEmailResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *SignInByEmailResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *SignInByEmailResData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 手机已注册检查
type PhoneValidCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneInfo *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
}

func (x *PhoneValidCheckReq) Reset() {
	*x = PhoneValidCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneValidCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneValidCheckReq) ProtoMessage() {}

func (x *PhoneValidCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneValidCheckReq.ProtoReflect.Descriptor instead.
func (*PhoneValidCheckReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{17}
}

func (x *PhoneValidCheckReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

type PhoneValidCheckRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *PhoneValidCheckRes) Reset() {
	*x = PhoneValidCheckRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *PhoneValidCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoneValidCheckRes) ProtoMessage() {}

func (x *PhoneValidCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoneValidCheckRes.ProtoReflect.Descriptor instead.
func (*PhoneValidCheckRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{18}
}

func (x *PhoneValidCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *PhoneValidCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *PhoneValidCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 邮箱已注册检查
type EmailValidCheckReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
}

func (x *EmailValidCheckReq) Reset() {
	*x = EmailValidCheckReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailValidCheckReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailValidCheckReq) ProtoMessage() {}

func (x *EmailValidCheckReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailValidCheckReq.ProtoReflect.Descriptor instead.
func (*EmailValidCheckReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{19}
}

func (x *EmailValidCheckReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

type EmailValidCheckRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *EmailValidCheckRes) Reset() {
	*x = EmailValidCheckRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EmailValidCheckRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmailValidCheckRes) ProtoMessage() {}

func (x *EmailValidCheckRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmailValidCheckRes.ProtoReflect.Descriptor instead.
func (*EmailValidCheckRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{20}
}

func (x *EmailValidCheckRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *EmailValidCheckRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *EmailValidCheckRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type RefreshTokenReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *RefreshTokenReq) Reset() {
	*x = RefreshTokenReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenReq) ProtoMessage() {}

func (x *RefreshTokenReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenReq.ProtoReflect.Descriptor instead.
func (*RefreshTokenReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{21}
}

func (x *RefreshTokenReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type RefreshTokenRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32                `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string               `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error        `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *RefreshTokenResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *RefreshTokenRes) Reset() {
	*x = RefreshTokenRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenRes) ProtoMessage() {}

func (x *RefreshTokenRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenRes.ProtoReflect.Descriptor instead.
func (*RefreshTokenRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{22}
}

func (x *RefreshTokenRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *RefreshTokenRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RefreshTokenRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *RefreshTokenRes) GetData() *RefreshTokenResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type RefreshTokenResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
}

func (x *RefreshTokenResData) Reset() {
	*x = RefreshTokenResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RefreshTokenResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RefreshTokenResData) ProtoMessage() {}

func (x *RefreshTokenResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RefreshTokenResData.ProtoReflect.Descriptor instead.
func (*RefreshTokenResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{23}
}

func (x *RefreshTokenResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type UserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *UserInfoReq) Reset() {
	*x = UserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoReq) ProtoMessage() {}

func (x *UserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoReq.ProtoReflect.Descriptor instead.
func (*UserInfoReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{24}
}

type UserInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32            `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string           `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error    `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *UserInfoResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *UserInfoRes) Reset() {
	*x = UserInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoRes) ProtoMessage() {}

func (x *UserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoRes.ProtoReflect.Descriptor instead.
func (*UserInfoRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{25}
}

func (x *UserInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UserInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UserInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *UserInfoRes) GetData() *UserInfoResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserInfoResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	UserInfo *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
}

func (x *UserInfoResData) Reset() {
	*x = UserInfoResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UserInfoResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfoResData) ProtoMessage() {}

func (x *UserInfoResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfoResData.ProtoReflect.Descriptor instead.
func (*UserInfoResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{26}
}

func (x *UserInfoResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

type UpdateUserInfoReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	FirstName  string `protobuf:"bytes,1,opt,name=first_name,json=firstName,proto3" json:"first_name,omitempty"`
	MiddleName string `protobuf:"bytes,2,opt,name=middle_name,json=middleName,proto3" json:"middle_name,omitempty"`
	LastName   string `protobuf:"bytes,3,opt,name=last_name,json=lastName,proto3" json:"last_name,omitempty"`
	Nickname   string `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty" dc:"昵称"`                            // 昵称
	Gender     Gender `protobuf:"varint,5,opt,name=gender,proto3,enum=user.v1.Gender" json:"gender,omitempty" dc:"性别：0未知 1男 2女"` // 性别：0未知 1男 2女
	Avatar     string `protobuf:"bytes,6,opt,name=avatar,proto3" json:"avatar,omitempty" dc:"头像url"`                             // 头像url
}

func (x *UpdateUserInfoReq) Reset() {
	*x = UpdateUserInfoReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserInfoReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoReq) ProtoMessage() {}

func (x *UpdateUserInfoReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoReq.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{27}
}

func (x *UpdateUserInfoReq) GetFirstName() string {
	if x != nil {
		return x.FirstName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetMiddleName() string {
	if x != nil {
		return x.MiddleName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetLastName() string {
	if x != nil {
		return x.LastName
	}
	return ""
}

func (x *UpdateUserInfoReq) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UpdateUserInfoReq) GetGender() Gender {
	if x != nil {
		return x.Gender
	}
	return Gender_UNKNOWN
}

func (x *UpdateUserInfoReq) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

type UpdateUserInfoRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *UpdateUserInfoRes) Reset() {
	*x = UpdateUserInfoRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateUserInfoRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserInfoRes) ProtoMessage() {}

func (x *UpdateUserInfoRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserInfoRes.ProtoReflect.Descriptor instead.
func (*UpdateUserInfoRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateUserInfoRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *UpdateUserInfoRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *UpdateUserInfoRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 请求结构：发送验证码
type SendVerifyCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 手机信息（包含手机号和国家码等）
	PhoneInfo *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty" dc:"手机信息（包含手机号和国家码等）"`
	// 指定发送验证码的渠道（短信、WhatsApp、语音等）
	VerifyCodeChannel VerifyCodeChannel `protobuf:"varint,2,opt,name=verify_code_channel,json=verifyCodeChannel,proto3,enum=user.v1.VerifyCodeChannel" json:"verify_code_channel,omitempty" dc:"指定发送验证码的渠道（短信、WhatsApp、语音等）"`
	// 指定验证码使用的业务场景（登录、注册、重置密码等）
	VerifyCodeScene VerifyCodeScene `protobuf:"varint,3,opt,name=verify_code_scene,json=verifyCodeScene,proto3,enum=user.v1.VerifyCodeScene" json:"verify_code_scene,omitempty" dc:"指定验证码使用的业务场景（登录、注册、重置密码等）"`
	// 邮箱
	Email string `protobuf:"bytes,4,opt,name=email,proto3" json:"email,omitempty" dc:"邮箱"`
}

func (x *SendVerifyCodeReq) Reset() {
	*x = SendVerifyCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendVerifyCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeReq) ProtoMessage() {}

func (x *SendVerifyCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeReq.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{29}
}

func (x *SendVerifyCodeReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *SendVerifyCodeReq) GetVerifyCodeChannel() VerifyCodeChannel {
	if x != nil {
		return x.VerifyCodeChannel
	}
	return VerifyCodeChannel_UNSPECIFIED
}

func (x *SendVerifyCodeReq) GetVerifyCodeScene() VerifyCodeScene {
	if x != nil {
		return x.VerifyCodeScene
	}
	return VerifyCodeScene_VERIFY_CODE_SCENE_UNSPECIFIED
}

func (x *SendVerifyCodeReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// 响应结构：发送验证码结果
type SendVerifyCodeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态码，0 表示成功，其他为错误码（用于简单兼容处理）
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty" dc:"状态码，0 表示成功，其他为错误码（用于简单兼容处理）"`
	// 状态消息，一般用于错误描述或提示信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty" dc:"状态消息，一般用于错误描述或提示信息"`
	// 通用错误结构，包含错误码、错误描述、详细字段错误等（可选）
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty" dc:"通用错误结构，包含错误码、错误描述、详细字段错误等（可选）"`
}

func (x *SendVerifyCodeRes) Reset() {
	*x = SendVerifyCodeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SendVerifyCodeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVerifyCodeRes) ProtoMessage() {}

func (x *SendVerifyCodeRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVerifyCodeRes.ProtoReflect.Descriptor instead.
func (*SendVerifyCodeRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{30}
}

func (x *SendVerifyCodeRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SendVerifyCodeRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SendVerifyCodeRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 验证验证码
type VerifyCodeReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OptCode string `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	// 指定验证码使用的业务场景（登录、注册、重置密码等）， 这里只能填MY_PHONE
	VerifyCodeScene VerifyCodeScene `protobuf:"varint,3,opt,name=verify_code_scene,json=verifyCodeScene,proto3,enum=user.v1.VerifyCodeScene" json:"verify_code_scene,omitempty" dc:"指定验证码使用的业务场景（登录、注册、重置密码等）， 这里只能填MY_PHONE"`
}

func (x *VerifyCodeReq) Reset() {
	*x = VerifyCodeReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyCodeReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCodeReq) ProtoMessage() {}

func (x *VerifyCodeReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCodeReq.ProtoReflect.Descriptor instead.
func (*VerifyCodeReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{31}
}

func (x *VerifyCodeReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *VerifyCodeReq) GetVerifyCodeScene() VerifyCodeScene {
	if x != nil {
		return x.VerifyCodeScene
	}
	return VerifyCodeScene_VERIFY_CODE_SCENE_UNSPECIFIED
}

type VerifyCodeRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 状态码，0 表示成功，其他为错误码（用于简单兼容处理）
	Code int32 `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty" dc:"状态码，0 表示成功，其他为错误码（用于简单兼容处理）"`
	// 状态消息，一般用于错误描述或提示信息
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty" dc:"状态消息，一般用于错误描述或提示信息"`
	// 通用错误结构，包含错误码、错误描述、详细字段错误等（可选）
	Error *common.Error      `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty" dc:"通用错误结构，包含错误码、错误描述、详细字段错误等（可选）"`
	Data  *VerifyCodeResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *VerifyCodeRes) Reset() {
	*x = VerifyCodeRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyCodeRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCodeRes) ProtoMessage() {}

func (x *VerifyCodeRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCodeRes.ProtoReflect.Descriptor instead.
func (*VerifyCodeRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{32}
}

func (x *VerifyCodeRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *VerifyCodeRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *VerifyCodeRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *VerifyCodeRes) GetData() *VerifyCodeResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type VerifyCodeResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResetToken string `protobuf:"bytes,1,opt,name=reset_token,json=resetToken,proto3" json:"reset_token,omitempty" dc:"用于下一步认证的reset_token"` // 用于下一步认证的reset_token
}

func (x *VerifyCodeResData) Reset() {
	*x = VerifyCodeResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VerifyCodeResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VerifyCodeResData) ProtoMessage() {}

func (x *VerifyCodeResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VerifyCodeResData.ProtoReflect.Descriptor instead.
func (*VerifyCodeResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{33}
}

func (x *VerifyCodeResData) GetResetToken() string {
	if x != nil {
		return x.ResetToken
	}
	return ""
}

// 默认头像列表请求
type AvatarListReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *AvatarListReq) Reset() {
	*x = AvatarListReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvatarListReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarListReq) ProtoMessage() {}

func (x *AvatarListReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarListReq.ProtoReflect.Descriptor instead.
func (*AvatarListReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{34}
}

// 默认头像列表响应
type AvatarListRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32           `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string          `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error   `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *AvatarListData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *AvatarListRes) Reset() {
	*x = AvatarListRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvatarListRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarListRes) ProtoMessage() {}

func (x *AvatarListRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarListRes.ProtoReflect.Descriptor instead.
func (*AvatarListRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{35}
}

func (x *AvatarListRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *AvatarListRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AvatarListRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *AvatarListRes) GetData() *AvatarListData {
	if x != nil {
		return x.Data
	}
	return nil
}

type AvatarListData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	List []*AvatarItem `protobuf:"bytes,1,rep,name=list,proto3" json:"list,omitempty"`
}

func (x *AvatarListData) Reset() {
	*x = AvatarListData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvatarListData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarListData) ProtoMessage() {}

func (x *AvatarListData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarListData.ProtoReflect.Descriptor instead.
func (*AvatarListData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{36}
}

func (x *AvatarListData) GetList() []*AvatarItem {
	if x != nil {
		return x.List
	}
	return nil
}

type AvatarItem struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Key  string `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	Url  string `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
}

func (x *AvatarItem) Reset() {
	*x = AvatarItem{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AvatarItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarItem) ProtoMessage() {}

func (x *AvatarItem) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarItem.ProtoReflect.Descriptor instead.
func (*AvatarItem) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{37}
}

func (x *AvatarItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AvatarItem) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *AvatarItem) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ChangePhoneReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	PhoneInfo  *common.PhoneInfo `protobuf:"bytes,1,opt,name=phone_info,json=phoneInfo,proto3" json:"phone_info,omitempty"`
	OptCode    string            `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	FrontInfo  *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	ResetToken string            `protobuf:"bytes,4,opt,name=reset_token,json=resetToken,proto3" json:"reset_token,omitempty" dc:"VerifyCode 返回的临时凭证"` // VerifyCode 返回的临时凭证
}

func (x *ChangePhoneReq) Reset() {
	*x = ChangePhoneReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePhoneReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePhoneReq) ProtoMessage() {}

func (x *ChangePhoneReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePhoneReq.ProtoReflect.Descriptor instead.
func (*ChangePhoneReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{38}
}

func (x *ChangePhoneReq) GetPhoneInfo() *common.PhoneInfo {
	if x != nil {
		return x.PhoneInfo
	}
	return nil
}

func (x *ChangePhoneReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *ChangePhoneReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

func (x *ChangePhoneReq) GetResetToken() string {
	if x != nil {
		return x.ResetToken
	}
	return ""
}

type ChangePhoneRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ChangePhoneRes) Reset() {
	*x = ChangePhoneRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePhoneRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePhoneRes) ProtoMessage() {}

func (x *ChangePhoneRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePhoneRes.ProtoReflect.Descriptor instead.
func (*ChangePhoneRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{39}
}

func (x *ChangePhoneRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangePhoneRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ChangePhoneRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 修改登录邮箱
// 第一步： SendVerifyCode（） 获取 reset_token
// 第二步： ChangeEmail（） 修改登录邮箱
type ChangeEmailReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Email      string            `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	OptCode    string            `protobuf:"bytes,2,opt,name=opt_code,json=optCode,proto3" json:"opt_code,omitempty"`
	FrontInfo  *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
	ResetToken string            `protobuf:"bytes,4,opt,name=reset_token,json=resetToken,proto3" json:"reset_token,omitempty" dc:"VerifyCode 返回的临时凭证"` // VerifyCode 返回的临时凭证
}

func (x *ChangeEmailReq) Reset() {
	*x = ChangeEmailReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeEmailReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailReq) ProtoMessage() {}

func (x *ChangeEmailReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailReq.ProtoReflect.Descriptor instead.
func (*ChangeEmailReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{40}
}

func (x *ChangeEmailReq) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *ChangeEmailReq) GetOptCode() string {
	if x != nil {
		return x.OptCode
	}
	return ""
}

func (x *ChangeEmailReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

func (x *ChangeEmailReq) GetResetToken() string {
	if x != nil {
		return x.ResetToken
	}
	return ""
}

type ChangeEmailRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ChangeEmailRes) Reset() {
	*x = ChangeEmailRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangeEmailRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangeEmailRes) ProtoMessage() {}

func (x *ChangeEmailRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangeEmailRes.ProtoReflect.Descriptor instead.
func (*ChangeEmailRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{41}
}

func (x *ChangeEmailRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangeEmailRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ChangeEmailRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

type LoginGoogleReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	IdToken   string            `protobuf:"bytes,1,opt,name=id_token,json=idToken,proto3" json:"id_token,omitempty"`
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *LoginGoogleReq) Reset() {
	*x = LoginGoogleReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginGoogleReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginGoogleReq) ProtoMessage() {}

func (x *LoginGoogleReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginGoogleReq.ProtoReflect.Descriptor instead.
func (*LoginGoogleReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{42}
}

func (x *LoginGoogleReq) GetIdToken() string {
	if x != nil {
		return x.IdToken
	}
	return ""
}

func (x *LoginGoogleReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type LoginGoogleRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32               `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string              `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error       `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	Data  *LoginGoogleResData `protobuf:"bytes,4,opt,name=data,proto3" json:"data,omitempty"`
}

func (x *LoginGoogleRes) Reset() {
	*x = LoginGoogleRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginGoogleRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginGoogleRes) ProtoMessage() {}

func (x *LoginGoogleRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginGoogleRes.ProtoReflect.Descriptor instead.
func (*LoginGoogleRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{43}
}

func (x *LoginGoogleRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *LoginGoogleRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *LoginGoogleRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

func (x *LoginGoogleRes) GetData() *LoginGoogleResData {
	if x != nil {
		return x.Data
	}
	return nil
}

type LoginGoogleResData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Token     string    `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty" dc:"登录成功后返回的会话 token"`           // 登录成功后返回的会话 token
	Secret    string    `protobuf:"bytes,2,opt,name=secret,proto3" json:"secret,omitempty" dc:"客户端存储的密钥，用于api签名和更新token"` // 客户端存储的密钥，用于api签名和更新token
	UserInfo  *UserInfo `protobuf:"bytes,3,opt,name=user_info,json=userInfo,proto3" json:"user_info,omitempty"`
	SessionId string    `protobuf:"bytes,4,opt,name=session_id,json=sessionId,proto3" json:"session_id,omitempty" dc:"客户端存储，用于api签名和更新token"` // 客户端存储，用于api签名和更新token
}

func (x *LoginGoogleResData) Reset() {
	*x = LoginGoogleResData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LoginGoogleResData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginGoogleResData) ProtoMessage() {}

func (x *LoginGoogleResData) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginGoogleResData.ProtoReflect.Descriptor instead.
func (*LoginGoogleResData) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{44}
}

func (x *LoginGoogleResData) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *LoginGoogleResData) GetSecret() string {
	if x != nil {
		return x.Secret
	}
	return ""
}

func (x *LoginGoogleResData) GetUserInfo() *UserInfo {
	if x != nil {
		return x.UserInfo
	}
	return nil
}

func (x *LoginGoogleResData) GetSessionId() string {
	if x != nil {
		return x.SessionId
	}
	return ""
}

// 首次设置密码（或从无密码态补设）
type SetPasswordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Password  string            `protobuf:"bytes,1,opt,name=password,proto3" json:"password,omitempty" dc:"密码"` // 密码
	FrontInfo *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *SetPasswordReq) Reset() {
	*x = SetPasswordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPasswordReq) ProtoMessage() {}

func (x *SetPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPasswordReq.ProtoReflect.Descriptor instead.
func (*SetPasswordReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{45}
}

func (x *SetPasswordReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *SetPasswordReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type SetPasswordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *SetPasswordRes) Reset() {
	*x = SetPasswordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SetPasswordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetPasswordRes) ProtoMessage() {}

func (x *SetPasswordRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetPasswordRes.ProtoReflect.Descriptor instead.
func (*SetPasswordRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{46}
}

func (x *SetPasswordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *SetPasswordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *SetPasswordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 已登录用户用旧密码修改
type ChangePasswordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	OldPassword string            `protobuf:"bytes,1,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty" dc:"旧密码"` // 旧密码
	NewPassword string            `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty" dc:"新密码"` // 新密码
	FrontInfo   *common.FrontInfo `protobuf:"bytes,3,opt,name=front_info,json=frontInfo,proto3" json:"front_info,omitempty"`
}

func (x *ChangePasswordReq) Reset() {
	*x = ChangePasswordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordReq) ProtoMessage() {}

func (x *ChangePasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordReq.ProtoReflect.Descriptor instead.
func (*ChangePasswordReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{47}
}

func (x *ChangePasswordReq) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangePasswordReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *ChangePasswordReq) GetFrontInfo() *common.FrontInfo {
	if x != nil {
		return x.FrontInfo
	}
	return nil
}

type ChangePasswordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ChangePasswordRes) Reset() {
	*x = ChangePasswordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ChangePasswordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordRes) ProtoMessage() {}

func (x *ChangePasswordRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordRes.ProtoReflect.Descriptor instead.
func (*ChangePasswordRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{48}
}

func (x *ChangePasswordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ChangePasswordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ChangePasswordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

// 两步式重置：用 reset_token 设置新密码
// 第一步： SendVerifyCode（） 获取 reset_token
// 第二步： ResetPassword（） 重置密码
type ResetPasswordReq struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ResetToken  string `protobuf:"bytes,1,opt,name=reset_token,json=resetToken,proto3" json:"reset_token,omitempty" dc:"来自 Verify 的短时令牌"` // 来自 Verify 的短时令牌
	NewPassword string `protobuf:"bytes,2,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"`
}

func (x *ResetPasswordReq) Reset() {
	*x = ResetPasswordReq{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordReq) ProtoMessage() {}

func (x *ResetPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordReq.ProtoReflect.Descriptor instead.
func (*ResetPasswordReq) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{49}
}

func (x *ResetPasswordReq) GetResetToken() string {
	if x != nil {
		return x.ResetToken
	}
	return ""
}

func (x *ResetPasswordReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

type ResetPasswordRes struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Code  int32         `protobuf:"varint,1,opt,name=code,proto3" json:"code,omitempty"`
	Msg   string        `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	Error *common.Error `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
}

func (x *ResetPasswordRes) Reset() {
	*x = ResetPasswordRes{}
	if protoimpl.UnsafeEnabled {
		mi := &file_user_v1_user_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResetPasswordRes) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordRes) ProtoMessage() {}

func (x *ResetPasswordRes) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordRes.ProtoReflect.Descriptor instead.
func (*ResetPasswordRes) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{50}
}

func (x *ResetPasswordRes) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ResetPasswordRes) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ResetPasswordRes) GetError() *common.Error {
	if x != nil {
		return x.Error
	}
	return nil
}

var File_user_v1_user_proto protoreflect.FileDescriptor

var file_user_v1_user_proto_rawDesc = []byte{
	0x0a, 0x12, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x07, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x61, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0xde, 0x03, 0x0a, 0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12,
	0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e, 0x69,
	0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x72, 0x65, 0x61, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x72, 0x65, 0x61,
	0x43, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75,
	0x6d, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75,
	0x6d, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x69, 0x6e, 0x64, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x62, 0x69, 0x6e, 0x64, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12,
	0x24, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x72, 0x65, 0x61, 0x6c, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0c, 0x62, 0x69, 0x6e, 0x64, 0x52, 0x65, 0x61,
	0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x73, 0x5f, 0x67,
	0x75, 0x65, 0x73, 0x74, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x73, 0x47, 0x75,
	0x65, 0x73, 0x74, 0x22, 0x41, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x71,
	0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x56, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x42,
	0x0a, 0x0e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72,
	0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x8b, 0x01, 0x0a, 0x0e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x22, 0xaa, 0x01, 0x0a, 0x09, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x12, 0x18,
	0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x35, 0x0a, 0x0c, 0x73, 0x69, 0x67, 0x6e, 0x5f, 0x69,
	0x6e, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x13, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x0a, 0x73, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x22, 0x8a, 0x01,
	0x0a, 0x0d, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x90, 0x01, 0x0a, 0x11, 0x55,
	0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61,
	0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x2e,
	0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x7c, 0x0a,
	0x12, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f,
	0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x94, 0x01, 0x0a, 0x12,
	0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x33, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x22, 0x76, 0x0a, 0x16, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x91, 0x01, 0x0a, 0x10, 0x53,
	0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x30, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x90,
	0x01, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x31,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x93, 0x01, 0x0a, 0x14, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50, 0x68,
	0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x75, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x49,
	0x6e, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x90,
	0x01, 0x0a, 0x10, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x31,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74,
	0x61, 0x22, 0x93, 0x01, 0x0a, 0x14, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08,
	0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73,
	0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65,
	0x73, 0x73, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x22, 0x46, 0x0a, 0x12, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x30, 0x0a,
	0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x5f, 0x0a, 0x12, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65,
	0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0x2a, 0x0a, 0x12, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5f, 0x0a, 0x12,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x43, 0x0a,
	0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x71,
	0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72,
	0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x8e, 0x01, 0x0a, 0x0f, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73,
	0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05,
	0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73,
	0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64,
	0x61, 0x74, 0x61, 0x22, 0x2b, 0x0a, 0x13, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x0d, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x22,
	0x86, 0x01, 0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x44, 0x61,
	0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x41, 0x0a, 0x0f, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x2e, 0x0a, 0x09, 0x75,
	0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xcd, 0x01, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x12, 0x1d, 0x0a, 0x0a, 0x66, 0x69, 0x72, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x66, 0x69, 0x72, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x69, 0x64, 0x64, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x61, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x61, 0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a,
	0x0a, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x6e, 0x69, 0x63, 0x6b, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x06, 0x67, 0x65,
	0x6e, 0x64, 0x65, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x0f, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x22, 0x5e, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0xed, 0x01, 0x0a, 0x11,
	0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65,
	0x71, 0x12, 0x30, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x4a, 0x0a, 0x13, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f,
	0x64, 0x65, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x52, 0x11, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x44, 0x0a, 0x11, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73,
	0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53,
	0x63, 0x65, 0x6e, 0x65, 0x52, 0x0f, 0x76, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x53, 0x63, 0x65, 0x6e, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x22, 0x5e, 0x0a, 0x11, 0x53,
	0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x70, 0x0a, 0x0d, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x70, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x44, 0x0a, 0x11, 0x76, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x73, 0x63, 0x65, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72,
	0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x52, 0x0f, 0x76, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x22, 0x8a, 0x01,
	0x0a, 0x0d, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72,
	0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x34, 0x0a, 0x11, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0x0f, 0x0a, 0x0d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x71, 0x22, 0x87, 0x01, 0x0a, 0x0d, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2b,
	0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73,
	0x74, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x39, 0x0a, 0x0e, 0x41,
	0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x61, 0x74, 0x61, 0x12, 0x27, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x49, 0x74, 0x65, 0x6d,
	0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x22, 0x44, 0x0a, 0x0a, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72,
	0x49, 0x74, 0x65, 0x6d, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72,
	0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0xb0, 0x01, 0x0a,
	0x0e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x12,
	0x30, 0x0a, 0x0a, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x50, 0x68, 0x6f,
	0x6e, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x30, 0x0a, 0x0a,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x5b, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x94, 0x01, 0x0a,
	0x0e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x12,
	0x14, 0x0a, 0x05, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x70, 0x74, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x70, 0x74, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72,
	0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x74, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x5b, 0x0a, 0x0e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72,
	0x22, 0x5d, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x64, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x64, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x30, 0x0a,
	0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x8c, 0x01, 0x0a, 0x0e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x12, 0x2f, 0x0a,
	0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x44, 0x61, 0x74, 0x61, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x91,
	0x01, 0x0a, 0x12, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65,
	0x73, 0x44, 0x61, 0x74, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x2e, 0x0a, 0x09, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x69,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x5e, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x71, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x30, 0x0a, 0x0a, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72,
	0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x22, 0x5b, 0x0a, 0x0e, 0x53, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72,
	0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22,
	0x8b, 0x01, 0x0a, 0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x71, 0x12, 0x21, 0x0a, 0x0c, 0x6f, 0x6c, 0x64, 0x5f, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6f, 0x6c, 0x64,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f,
	0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x6e, 0x65, 0x77, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x30, 0x0a, 0x0a, 0x66,
	0x72, 0x6f, 0x6e, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x46, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x5e, 0x0a,
	0x11, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12, 0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x22, 0x56, 0x0a,
	0x10, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x65, 0x74, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x77, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x77, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x5d, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6d, 0x73, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x73, 0x67, 0x12,
	0x23, 0x0a, 0x05, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x0d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x45, 0x72, 0x72, 0x6f, 0x72, 0x52, 0x05, 0x65,
	0x72, 0x72, 0x6f, 0x72, 0x2a, 0x2b, 0x0a, 0x06, 0x47, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x0b,
	0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4d,
	0x41, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x45, 0x4d, 0x41, 0x4c, 0x45, 0x10,
	0x02, 0x2a, 0x58, 0x0a, 0x0a, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x18, 0x0a, 0x14, 0x53, 0x49, 0x47,
	0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x41, 0x43, 0x43, 0x4f, 0x55, 0x4e,
	0x54, 0x10, 0x01, 0x12, 0x16, 0x0a, 0x12, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x49, 0x4e, 0x5f, 0x54,
	0x59, 0x50, 0x45, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x02, 0x2a, 0x8c, 0x01, 0x0a, 0x0f,
	0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x53, 0x63, 0x65, 0x6e, 0x65, 0x12,
	0x21, 0x0a, 0x1d, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43, 0x4f, 0x44, 0x45, 0x5f, 0x53,
	0x43, 0x45, 0x4e, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44,
	0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x4c, 0x4f, 0x47, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x0b, 0x0a,
	0x07, 0x53, 0x49, 0x47, 0x4e, 0x5f, 0x55, 0x50, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45,
	0x53, 0x45, 0x54, 0x5f, 0x50, 0x41, 0x53, 0x53, 0x57, 0x4f, 0x52, 0x44, 0x10, 0x03, 0x12, 0x0e,
	0x0a, 0x0a, 0x42, 0x49, 0x4e, 0x44, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x04, 0x12, 0x0c,
	0x0a, 0x08, 0x4d, 0x59, 0x5f, 0x50, 0x48, 0x4f, 0x4e, 0x45, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08,
	0x4d, 0x59, 0x5f, 0x45, 0x4d, 0x41, 0x49, 0x4c, 0x10, 0x06, 0x2a, 0x51, 0x0a, 0x11, 0x56, 0x65,
	0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x43, 0x68, 0x61, 0x6e, 0x6e, 0x65, 0x6c, 0x12,
	0x0f, 0x0a, 0x0b, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x53, 0x4d, 0x53, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x57, 0x48, 0x41,
	0x54, 0x53, 0x41, 0x50, 0x50, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x4d, 0x41, 0x49, 0x4c,
	0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x4f, 0x49, 0x43, 0x45, 0x10, 0x04, 0x32, 0xcc, 0x0a,
	0x0a, 0x0b, 0x55, 0x73, 0x65, 0x72, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x3f, 0x0a,
	0x0b, 0x47, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x12, 0x17, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x75, 0x65, 0x73, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x48,
	0x0a, 0x0e, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65,
	0x12, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x56,
	0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66,
	0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x12, 0x3c, 0x0a, 0x0a, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x16,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x06, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70,
	0x12, 0x12, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x55,
	0x70, 0x52, 0x65, 0x71, 0x1a, 0x12, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x73, 0x12, 0x34, 0x0a, 0x06, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x12, 0x12, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67,
	0x6e, 0x49, 0x6e, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x55, 0x73, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x4b,
	0x0a, 0x0f, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e,
	0x49, 0x6e, 0x42, 0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x1b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42,
	0x79, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x53,
	0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x2e, 0x75,
	0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50,
	0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x71, 0x1a, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52,
	0x65, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x12, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69,
	0x67, 0x6e, 0x49, 0x6e, 0x42, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x1a, 0x19,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x69, 0x67, 0x6e, 0x49, 0x6e, 0x42,
	0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x42, 0x0a, 0x0c, 0x52, 0x65, 0x66,
	0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x52, 0x65, 0x71, 0x1a, 0x18, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65,
	0x66, 0x72, 0x65, 0x73, 0x68, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x52, 0x65, 0x73, 0x12, 0x36, 0x0a,
	0x08, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x14, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x1a,
	0x14, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x12,
	0x3c, 0x0a, 0x0a, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x12, 0x16, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69,
	0x73, 0x74, 0x52, 0x65, 0x71, 0x1a, 0x16, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x76, 0x61, 0x74, 0x61, 0x72, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a,
	0x0f, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x12, 0x1b, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65,
	0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x56, 0x61, 0x6c,
	0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52,
	0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x50, 0x68, 0x6f, 0x6e, 0x65, 0x52, 0x65, 0x73, 0x12, 0x4b, 0x0a, 0x0f, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x1b,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x61,
	0x6c, 0x69, 0x64, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x71, 0x1a, 0x1b, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x56, 0x61, 0x6c, 0x69, 0x64,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x43, 0x68, 0x61, 0x6e,
	0x67, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71,
	0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67,
	0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x4c, 0x6f, 0x67,
	0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65,
	0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x67, 0x69,
	0x6e, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x12, 0x3f, 0x0a, 0x0b, 0x53, 0x65,
	0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52,
	0x65, 0x71, 0x1a, 0x17, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x65, 0x74,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0e, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x1a, 0x2e,
	0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x71, 0x1a, 0x1a, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x2e, 0x76, 0x31, 0x2e, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x52, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61,
	0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x73, 0x65, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65,
	0x71, 0x1a, 0x19, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x65,
	0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x65, 0x73, 0x42, 0x2f, 0x5a, 0x2d,
	0x68, 0x61, 0x6c, 0x61, 0x6c, 0x70, 0x6c, 0x75, 0x73, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x2d, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x2d, 0x73, 0x76, 0x63, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData = file_user_v1_user_proto_rawDesc
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(file_user_v1_user_proto_rawDescData)
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 51)
var file_user_v1_user_proto_goTypes = []interface{}{
	(Gender)(0),                    // 0: user.v1.Gender
	(SignInType)(0),                // 1: user.v1.SignInType
	(VerifyCodeScene)(0),           // 2: user.v1.VerifyCodeScene
	(VerifyCodeChannel)(0),         // 3: user.v1.VerifyCodeChannel
	(*UserInfo)(nil),               // 4: user.v1.UserInfo
	(*SignUpReq)(nil),              // 5: user.v1.SignUpReq
	(*SignUpRes)(nil),              // 6: user.v1.SignUpRes
	(*GuestSignInReq)(nil),         // 7: user.v1.GuestSignInReq
	(*GuestSignInRes)(nil),         // 8: user.v1.GuestSignInRes
	(*SignInReq)(nil),              // 9: user.v1.SignInReq
	(*UserSignInRes)(nil),          // 10: user.v1.UserSignInRes
	(*UserSignInResData)(nil),      // 11: user.v1.UserSignInResData
	(*SignInByAccountReq)(nil),     // 12: user.v1.SignInByAccountReq
	(*SignInByAccountRes)(nil),     // 13: user.v1.SignInByAccountRes
	(*SignInByAccountResData)(nil), // 14: user.v1.SignInByAccountResData
	(*SignInByPhoneReq)(nil),       // 15: user.v1.SignInByPhoneReq
	(*SignInByPhoneRes)(nil),       // 16: user.v1.SignInByPhoneRes
	(*SignInByPhoneResData)(nil),   // 17: user.v1.SignInByPhoneResData
	(*SignInByEmailReq)(nil),       // 18: user.v1.SignInByEmailReq
	(*SignInByEmailRes)(nil),       // 19: user.v1.SignInByEmailRes
	(*SignInByEmailResData)(nil),   // 20: user.v1.SignInByEmailResData
	(*PhoneValidCheckReq)(nil),     // 21: user.v1.PhoneValidCheckReq
	(*PhoneValidCheckRes)(nil),     // 22: user.v1.PhoneValidCheckRes
	(*EmailValidCheckReq)(nil),     // 23: user.v1.EmailValidCheckReq
	(*EmailValidCheckRes)(nil),     // 24: user.v1.EmailValidCheckRes
	(*RefreshTokenReq)(nil),        // 25: user.v1.RefreshTokenReq
	(*RefreshTokenRes)(nil),        // 26: user.v1.RefreshTokenRes
	(*RefreshTokenResData)(nil),    // 27: user.v1.RefreshTokenResData
	(*UserInfoReq)(nil),            // 28: user.v1.UserInfoReq
	(*UserInfoRes)(nil),            // 29: user.v1.UserInfoRes
	(*UserInfoResData)(nil),        // 30: user.v1.UserInfoResData
	(*UpdateUserInfoReq)(nil),      // 31: user.v1.UpdateUserInfoReq
	(*UpdateUserInfoRes)(nil),      // 32: user.v1.UpdateUserInfoRes
	(*SendVerifyCodeReq)(nil),      // 33: user.v1.SendVerifyCodeReq
	(*SendVerifyCodeRes)(nil),      // 34: user.v1.SendVerifyCodeRes
	(*VerifyCodeReq)(nil),          // 35: user.v1.VerifyCodeReq
	(*VerifyCodeRes)(nil),          // 36: user.v1.VerifyCodeRes
	(*VerifyCodeResData)(nil),      // 37: user.v1.VerifyCodeResData
	(*AvatarListReq)(nil),          // 38: user.v1.AvatarListReq
	(*AvatarListRes)(nil),          // 39: user.v1.AvatarListRes
	(*AvatarListData)(nil),         // 40: user.v1.AvatarListData
	(*AvatarItem)(nil),             // 41: user.v1.AvatarItem
	(*ChangePhoneReq)(nil),         // 42: user.v1.ChangePhoneReq
	(*ChangePhoneRes)(nil),         // 43: user.v1.ChangePhoneRes
	(*ChangeEmailReq)(nil),         // 44: user.v1.ChangeEmailReq
	(*ChangeEmailRes)(nil),         // 45: user.v1.ChangeEmailRes
	(*LoginGoogleReq)(nil),         // 46: user.v1.LoginGoogleReq
	(*LoginGoogleRes)(nil),         // 47: user.v1.LoginGoogleRes
	(*LoginGoogleResData)(nil),     // 48: user.v1.LoginGoogleResData
	(*SetPasswordReq)(nil),         // 49: user.v1.SetPasswordReq
	(*SetPasswordRes)(nil),         // 50: user.v1.SetPasswordRes
	(*ChangePasswordReq)(nil),      // 51: user.v1.ChangePasswordReq
	(*ChangePasswordRes)(nil),      // 52: user.v1.ChangePasswordRes
	(*ResetPasswordReq)(nil),       // 53: user.v1.ResetPasswordReq
	(*ResetPasswordRes)(nil),       // 54: user.v1.ResetPasswordRes
	(*common.Error)(nil),           // 55: common.Error
	(*common.FrontInfo)(nil),       // 56: common.FrontInfo
	(*common.PhoneInfo)(nil),       // 57: common.PhoneInfo
}
var file_user_v1_user_proto_depIdxs = []int32{
	0,  // 0: user.v1.UserInfo.gender:type_name -> user.v1.Gender
	55, // 1: user.v1.SignUpRes.error:type_name -> common.Error
	56, // 2: user.v1.GuestSignInReq.front_info:type_name -> common.FrontInfo
	55, // 3: user.v1.GuestSignInRes.error:type_name -> common.Error
	11, // 4: user.v1.GuestSignInRes.data:type_name -> user.v1.UserSignInResData
	56, // 5: user.v1.SignInReq.front_info:type_name -> common.FrontInfo
	1,  // 6: user.v1.SignInReq.sign_in_type:type_name -> user.v1.SignInType
	55, // 7: user.v1.UserSignInRes.error:type_name -> common.Error
	11, // 8: user.v1.UserSignInRes.data:type_name -> user.v1.UserSignInResData
	4,  // 9: user.v1.UserSignInResData.user_info:type_name -> user.v1.UserInfo
	56, // 10: user.v1.SignInByAccountReq.front_info:type_name -> common.FrontInfo
	55, // 11: user.v1.SignInByAccountRes.error:type_name -> common.Error
	14, // 12: user.v1.SignInByAccountRes.data:type_name -> user.v1.SignInByAccountResData
	4,  // 13: user.v1.SignInByAccountResData.user_info:type_name -> user.v1.UserInfo
	57, // 14: user.v1.SignInByPhoneReq.phone_info:type_name -> common.PhoneInfo
	56, // 15: user.v1.SignInByPhoneReq.front_info:type_name -> common.FrontInfo
	55, // 16: user.v1.SignInByPhoneRes.error:type_name -> common.Error
	17, // 17: user.v1.SignInByPhoneRes.data:type_name -> user.v1.SignInByPhoneResData
	4,  // 18: user.v1.SignInByPhoneResData.user_info:type_name -> user.v1.UserInfo
	56, // 19: user.v1.SignInByEmailReq.front_info:type_name -> common.FrontInfo
	55, // 20: user.v1.SignInByEmailRes.error:type_name -> common.Error
	20, // 21: user.v1.SignInByEmailRes.data:type_name -> user.v1.SignInByEmailResData
	4,  // 22: user.v1.SignInByEmailResData.user_info:type_name -> user.v1.UserInfo
	57, // 23: user.v1.PhoneValidCheckReq.phone_info:type_name -> common.PhoneInfo
	55, // 24: user.v1.PhoneValidCheckRes.error:type_name -> common.Error
	55, // 25: user.v1.EmailValidCheckRes.error:type_name -> common.Error
	56, // 26: user.v1.RefreshTokenReq.front_info:type_name -> common.FrontInfo
	55, // 27: user.v1.RefreshTokenRes.error:type_name -> common.Error
	27, // 28: user.v1.RefreshTokenRes.data:type_name -> user.v1.RefreshTokenResData
	55, // 29: user.v1.UserInfoRes.error:type_name -> common.Error
	30, // 30: user.v1.UserInfoRes.data:type_name -> user.v1.UserInfoResData
	4,  // 31: user.v1.UserInfoResData.user_info:type_name -> user.v1.UserInfo
	0,  // 32: user.v1.UpdateUserInfoReq.gender:type_name -> user.v1.Gender
	55, // 33: user.v1.UpdateUserInfoRes.error:type_name -> common.Error
	57, // 34: user.v1.SendVerifyCodeReq.phone_info:type_name -> common.PhoneInfo
	3,  // 35: user.v1.SendVerifyCodeReq.verify_code_channel:type_name -> user.v1.VerifyCodeChannel
	2,  // 36: user.v1.SendVerifyCodeReq.verify_code_scene:type_name -> user.v1.VerifyCodeScene
	55, // 37: user.v1.SendVerifyCodeRes.error:type_name -> common.Error
	2,  // 38: user.v1.VerifyCodeReq.verify_code_scene:type_name -> user.v1.VerifyCodeScene
	55, // 39: user.v1.VerifyCodeRes.error:type_name -> common.Error
	37, // 40: user.v1.VerifyCodeRes.data:type_name -> user.v1.VerifyCodeResData
	55, // 41: user.v1.AvatarListRes.error:type_name -> common.Error
	40, // 42: user.v1.AvatarListRes.data:type_name -> user.v1.AvatarListData
	41, // 43: user.v1.AvatarListData.list:type_name -> user.v1.AvatarItem
	57, // 44: user.v1.ChangePhoneReq.phone_info:type_name -> common.PhoneInfo
	56, // 45: user.v1.ChangePhoneReq.front_info:type_name -> common.FrontInfo
	55, // 46: user.v1.ChangePhoneRes.error:type_name -> common.Error
	56, // 47: user.v1.ChangeEmailReq.front_info:type_name -> common.FrontInfo
	55, // 48: user.v1.ChangeEmailRes.error:type_name -> common.Error
	56, // 49: user.v1.LoginGoogleReq.front_info:type_name -> common.FrontInfo
	55, // 50: user.v1.LoginGoogleRes.error:type_name -> common.Error
	48, // 51: user.v1.LoginGoogleRes.data:type_name -> user.v1.LoginGoogleResData
	4,  // 52: user.v1.LoginGoogleResData.user_info:type_name -> user.v1.UserInfo
	56, // 53: user.v1.SetPasswordReq.front_info:type_name -> common.FrontInfo
	55, // 54: user.v1.SetPasswordRes.error:type_name -> common.Error
	56, // 55: user.v1.ChangePasswordReq.front_info:type_name -> common.FrontInfo
	55, // 56: user.v1.ChangePasswordRes.error:type_name -> common.Error
	55, // 57: user.v1.ResetPasswordRes.error:type_name -> common.Error
	7,  // 58: user.v1.UserService.GuestSignIn:input_type -> user.v1.GuestSignInReq
	33, // 59: user.v1.UserService.SendVerifyCode:input_type -> user.v1.SendVerifyCodeReq
	35, // 60: user.v1.UserService.VerifyCode:input_type -> user.v1.VerifyCodeReq
	5,  // 61: user.v1.UserService.SignUp:input_type -> user.v1.SignUpReq
	9,  // 62: user.v1.UserService.SignIn:input_type -> user.v1.SignInReq
	12, // 63: user.v1.UserService.SignInByAccount:input_type -> user.v1.SignInByAccountReq
	15, // 64: user.v1.UserService.SignInByPhone:input_type -> user.v1.SignInByPhoneReq
	18, // 65: user.v1.UserService.SignInByEmail:input_type -> user.v1.SignInByEmailReq
	25, // 66: user.v1.UserService.RefreshToken:input_type -> user.v1.RefreshTokenReq
	28, // 67: user.v1.UserService.UserInfo:input_type -> user.v1.UserInfoReq
	31, // 68: user.v1.UserService.UpdateUserInfo:input_type -> user.v1.UpdateUserInfoReq
	38, // 69: user.v1.UserService.AvatarList:input_type -> user.v1.AvatarListReq
	21, // 70: user.v1.UserService.PhoneValidCheck:input_type -> user.v1.PhoneValidCheckReq
	42, // 71: user.v1.UserService.ChangePhone:input_type -> user.v1.ChangePhoneReq
	23, // 72: user.v1.UserService.EmailValidCheck:input_type -> user.v1.EmailValidCheckReq
	44, // 73: user.v1.UserService.ChangeEmail:input_type -> user.v1.ChangeEmailReq
	46, // 74: user.v1.UserService.LoginGoogle:input_type -> user.v1.LoginGoogleReq
	49, // 75: user.v1.UserService.SetPassword:input_type -> user.v1.SetPasswordReq
	51, // 76: user.v1.UserService.ChangePassword:input_type -> user.v1.ChangePasswordReq
	53, // 77: user.v1.UserService.ResetPassword:input_type -> user.v1.ResetPasswordReq
	8,  // 78: user.v1.UserService.GuestSignIn:output_type -> user.v1.GuestSignInRes
	34, // 79: user.v1.UserService.SendVerifyCode:output_type -> user.v1.SendVerifyCodeRes
	36, // 80: user.v1.UserService.VerifyCode:output_type -> user.v1.VerifyCodeRes
	6,  // 81: user.v1.UserService.SignUp:output_type -> user.v1.SignUpRes
	10, // 82: user.v1.UserService.SignIn:output_type -> user.v1.UserSignInRes
	13, // 83: user.v1.UserService.SignInByAccount:output_type -> user.v1.SignInByAccountRes
	16, // 84: user.v1.UserService.SignInByPhone:output_type -> user.v1.SignInByPhoneRes
	19, // 85: user.v1.UserService.SignInByEmail:output_type -> user.v1.SignInByEmailRes
	26, // 86: user.v1.UserService.RefreshToken:output_type -> user.v1.RefreshTokenRes
	29, // 87: user.v1.UserService.UserInfo:output_type -> user.v1.UserInfoRes
	32, // 88: user.v1.UserService.UpdateUserInfo:output_type -> user.v1.UpdateUserInfoRes
	39, // 89: user.v1.UserService.AvatarList:output_type -> user.v1.AvatarListRes
	22, // 90: user.v1.UserService.PhoneValidCheck:output_type -> user.v1.PhoneValidCheckRes
	43, // 91: user.v1.UserService.ChangePhone:output_type -> user.v1.ChangePhoneRes
	24, // 92: user.v1.UserService.EmailValidCheck:output_type -> user.v1.EmailValidCheckRes
	45, // 93: user.v1.UserService.ChangeEmail:output_type -> user.v1.ChangeEmailRes
	47, // 94: user.v1.UserService.LoginGoogle:output_type -> user.v1.LoginGoogleRes
	50, // 95: user.v1.UserService.SetPassword:output_type -> user.v1.SetPasswordRes
	52, // 96: user.v1.UserService.ChangePassword:output_type -> user.v1.ChangePasswordRes
	54, // 97: user.v1.UserService.ResetPassword:output_type -> user.v1.ResetPasswordRes
	78, // [78:98] is the sub-list for method output_type
	58, // [58:78] is the sub-list for method input_type
	58, // [58:58] is the sub-list for extension type_name
	58, // [58:58] is the sub-list for extension extendee
	0,  // [0:58] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_user_v1_user_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignUpReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignUpRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuestSignInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GuestSignInRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSignInRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserSignInResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByAccountReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByAccountRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByAccountResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByPhoneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByPhoneRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByPhoneResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByEmailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByEmailRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SignInByEmailResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneValidCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*PhoneValidCheckRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailValidCheckReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EmailValidCheckRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RefreshTokenResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UserInfoResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserInfoReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateUserInfoRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendVerifyCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SendVerifyCodeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyCodeReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyCodeRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VerifyCodeResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvatarListReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvatarListRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvatarListData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AvatarItem); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePhoneReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePhoneRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeEmailReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangeEmailRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginGoogleReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginGoogleRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LoginGoogleResData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPasswordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SetPasswordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePasswordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ChangePasswordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetPasswordReq); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_user_v1_user_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResetPasswordRes); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_user_v1_user_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   51,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		EnumInfos:         file_user_v1_user_proto_enumTypes,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_rawDesc = nil
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
